{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/qrcode": "^1.5.5", "axios": "^1.10.0", "clsx": "^2.1.1", "i18next": "^25.2.1", "lucide-react": "^0.523.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-router-dom": "^7.1.5", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.13.4", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}