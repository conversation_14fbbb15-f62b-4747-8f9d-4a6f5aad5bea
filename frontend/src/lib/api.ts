import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3010/api/v1'

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export interface LoginRequest {
  identify: string
  password: string
}

export interface LoginResponse {
  Token: string
  Expires: string
  IsSucceeded: boolean
}

export interface RegisterRequest {
  first_name: string
  last_name: string
  email: string
  password: string
  phone: string
  country_code: string
}

export interface Report {
  successed: number
  failed: number
}

export interface Message {
  id: string
  user_id: string
  reg_id: string
  content: string
  receiver: string
  send_time: string
  is_sent: boolean
  report_id: string
  status: string // 1-processing, 2-paused, 3-successed, 4-failed, 5-future message 6-canceled
  created_at: string
  updated_at: string
}

export interface MessagesResponse {
  messages: Message[]
}

export interface DashboardStats {
  total_messages: number      // Only success + failed
  success_messages: number
  failed_messages: number
  processing_messages: number
  paused_messages: number
  canceled_messages: number
}

export const authApi = {
  login: (data: LoginRequest): Promise<LoginResponse> =>
    api.post('/login', data).then(res => res.data),

  register: (data: RegisterRequest): Promise<string> =>
    api.post('/users', data).then(res => res.data),
}

export const messageApi = {
  getReports: (): Promise<Report> =>
    api.get('/messages/reports').then(res => res.data),
  
  getMessages: (params?: {
    status?: string
    page?: string
    per_page?: string
    initial_time?: string
    end_time?: string
  }): Promise<MessagesResponse> =>
    api.get('/messages', { params }).then(res => res.data),

  getDashboardStats: (params?: {
    status?: string
    initial_time?: string
    end_time?: string
  }): Promise<DashboardStats> =>
    api.get('/messages/dashboard-stats', { params }).then(res => res.data),
  
  sendMessage: (data: any) => {
    const config = data instanceof FormData
      ? { headers: { 'Content-Type': 'multipart/form-data' } }
      : {}
    return api.post('/messages/sendmessage', data, config).then(res => res.data)
  },

  cancelMessage: (messageId: string) =>
    api.delete(`/messages/${messageId}`).then(res => res.data),

  updateMessageStatus: (messageId: string, status: string) =>
    api.put(`/messages/${messageId}/status`, { status }).then(res => res.data),

  updateFutureMessage: (data: any) => {
    const config = data instanceof FormData
      ? { headers: { 'Content-Type': 'multipart/form-data' } }
      : {}
    return api.put('/messages/futuremessage', data, config).then(res => res.data)
  },

  deleteFutureMessage: (messageId: string) =>
    api.delete(`/messages/futuremessage?id=${messageId}`).then(res => res.data),

  pauseMessage: (messageId: string, isPause: boolean) =>
    api.put('/messages/queue', { msg_id: messageId, is_pause: isPause }).then(res => res.data),

  deleteQueueMessage: (messageId: string) =>
    api.delete(`/messages/queue?id=${messageId}`).then(res => res.data),
}

export const deviceApi = {
  getDevices: () =>
    api.get('/devices').then(res => res.data),

  getQr: () =>
    api.get('/devices/qr').then(res => res.data),

  getCode: (phone: string) =>
    api.post('/devices/code', { phone }).then(res => res.data),

  logout: (regId: string) =>
    api.post('/devices/logout', { reg_id: regId }).then(res => res.data),

  checkDevice: (regId: string) =>
    api.post(`/devices/check?reg_id=${regId}`).then(res => res.data),

  checkActiveDevice: (regId: string) =>
    api.post('/devices/check/active', { reg_id: regId }).then(res => res.data),
}

export const groupApi = {
  getGroups: () =>
    api.get('/groups').then(res => res.data),

  getGroupsWithStats: () =>
    api.get('/groups/stats').then(res => res.data),

  createGroup: (data: any) =>
    api.post('/groups', data).then(res => res.data),

  updateGroup: (id: string, data: any) =>
    api.put(`/groups/${id}`, data).then(res => res.data),

  deleteGroup: (id: string) =>
    api.delete(`/groups/${id}`).then(res => res.data),

  getGroupContacts: (id: string) =>
    api.get(`/groups/${id}/contacts`).then(res => res.data),

  addContact: (id: string, data: any) =>
    api.post(`/groups/${id}/contacts`, data).then(res => res.data),

  deleteContact: (contactId: string) =>
    api.delete(`/groups/contacts/${contactId}`).then(res => res.data),
}

export interface BlacklistItem {
  id: string
  user_id: string
  device_number: string
  country_code: string
  number: string
  created_at: string
  updated_at: string
}

export interface BlacklistStats {
  total_blacklisted: number
  added_this_month: number
  removed_this_month: number
}

export const blacklistApi = {
  addToBlacklist: (data: any) =>
    api.post('/blacklist', data).then(res => res.data),

  getBlacklist: (): Promise<BlacklistItem[]> =>
    api.get('/blacklist').then(res => res.data),

  getBlacklistStats: (): Promise<BlacklistStats> =>
    api.get('/blacklist/stats').then(res => res.data),

  removeFromBlacklist: (id: string) =>
    api.delete(`/blacklist/${id}`).then(res => res.data),
}

export const userApi = {
  getCurrentUser: () =>
    api.get('/users').then(res => res.data),

  updateUser: (data: any) =>
    api.put('/users', data).then(res => res.data),

  updateLanguage: (language: string): Promise<string> =>
    api.put('/users/language', { language }).then(res => res.data),

  updateAvatar: (avatar: string): Promise<string> =>
    api.put('/users/avatar', { avatar }).then(res => res.data),

  deleteUser: () =>
    api.delete('/users').then(res => res.data),
}
