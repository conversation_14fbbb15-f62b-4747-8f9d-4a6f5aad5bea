import React, { useState, useEffect } from 'react'
import { Clock, Calendar, CheckCircle, Search, Eye, Trash2, Play, Pause, Edit } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNotification } from '../../contexts/NotificationContext'
import { messageApi, Message } from '../../lib/api'
import { cn, formatDate } from '../../lib/utils'
import { Table, TableColumn } from '../../components/Table'
import ConfirmationModal from '../../components/ConfirmationModal'

interface MessagesProps {
  type: 'waiting' | 'future' | 'sent'
  title: string
  description: string
}

const getStatusMap = (t: any) => ({
  '1': { label: t('messages.processing'), color: 'bg-yellow-500/20 text-yellow-300' },
  '2': { label: t('messages.paused'), color: 'bg-gray-500/20 text-gray-300' },
  '3': { label: t('messages.sent'), color: 'bg-emerald-500/20 text-emerald-300' },
  '4': { label: t('messages.failed'), color: 'bg-red-500/20 text-red-300' },
  '5': { label: t('messages.future'), color: 'bg-blue-500/20 text-blue-300' },
  '6': { label: t('messages.canceled'), color: 'bg-gray-500/20 text-gray-300' },
})

export const Messages: React.FC<MessagesProps> = ({ type, title, description }) => {
  const { t } = useTranslation()
  const { showError, showSuccess } = useNotification()
  const statusMap = getStatusMap(t)
  const [messages, setMessages] = useState<Message[]>([])
  const [filteredMessages, setFilteredMessages] = useState<Message[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [messageToDelete, setMessageToDelete] = useState<Message | null>(null)
  const [showStatusConfirm, setShowStatusConfirm] = useState(false)
  const [messageToToggle, setMessageToToggle] = useState<Message | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [messageToEdit, setMessageToEdit] = useState<Message | null>(null)

  // Table columns definition
  const columns: TableColumn<Message>[] = [
    {
      key: 'receiver',
      title: t('messages.receiver'),
      sortable: true,
      render: (value) => (
        <span className="font-medium text-gray-200">{value}</span>
      )
    },
    {
      key: 'content',
      title: t('messages.content'),
      render: (value) => (
        <div className="max-w-xs">
          <div className="truncate text-gray-400" title={value}>{value}</div>
        </div>
      )
    },
    {
      key: 'status',
      title: t('messages.status'),
      sortable: true,
      render: (value) => (
        <span className={cn(
          "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
          statusMap[value as keyof typeof statusMap]?.color || 'bg-gray-500/20 text-gray-300'
        )}>
          {statusMap[value as keyof typeof statusMap]?.label || t('messages.unknown') || 'Unknown'}
        </span>
      )
    },
    {
      key: 'send_time',
      title: t('messages.sendTime'),
      sortable: true,
      render: (value) => (
        <span className="text-gray-400">
          {value ? formatDate(value) : '-'}
        </span>
      )
    },
    {
      key: 'created_at',
      title: t('messages.createdAt') || 'Created',
      sortable: true,
      render: (value) => (
        <span className="text-gray-400">{formatDate(value)}</span>
      )
    },
    {
      key: 'actions',
      title: t('messages.actions') || 'Actions',
      className: 'text-right',
      render: (_, message) => (
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={() => setSelectedMessage(message)}
            className="text-blue-400 hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-500/10"
            title={t('messages.viewDetails') || 'View details'}
          >
            <Eye size={16} />
          </button>

          {/* Edit Button - Only for future messages */}
          {type === 'future' && message.status === '5' && (
            <button
              onClick={() => handleEditMessage(message)}
              className="text-blue-400 hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-500/10"
              title={t('messages.edit') || 'Edit'}
            >
              <Edit size={16} />
            </button>
          )}

          {/* Status Toggle Button - Only for waiting messages */}
          {type === 'waiting' && (
            <button
              onClick={() => handleToggleStatus(message)}
              className="text-yellow-400 hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-500/10"
              title={String(message.status) === '2' ? t('messages.resume') : t('messages.pause')}
            >
              {String(message.status) === '2' ? <Play size={16} /> : <Pause size={16} />}
            </button>
          )}

          {/* Delete Button - For both waiting and future messages */}
          {(type === 'waiting' || type === 'future') && (
            <button
              onClick={() => handleDeleteMessage(message)}
              className="text-red-400 hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-500/10"
              title={t('messages.delete') || 'Delete'}
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      )
    }
  ]

  useEffect(() => {
    fetchMessages()
  }, [type])

  useEffect(() => {
    filterMessages()
  }, [messages, searchTerm, statusFilter, dateFilter])

  const fetchMessages = async () => {
    try {
      setIsLoading(true)
      let status = ''
      if (type === 'waiting') status = '1,2'
      else if (type === 'future') status = '5'
      else if (type === 'sent') status = '3,4'

      const data = await messageApi.getMessages({ status, per_page: '100' })
      setMessages(data?.messages || [])
    } catch (err: any) {
      showError(t('notifications.loadError'))
      console.error('Error fetching messages:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditMessage = (message: Message) => {
    setMessageToEdit(message)
    setShowEditModal(true)
  }

  const handleDeleteMessage = (message: Message) => {
    setMessageToDelete(message)
    setShowDeleteConfirm(true)
  }

  const confirmDeleteMessage = async () => {
    if (!messageToDelete) return

    try {
      // Use appropriate API based on message type
      if (type === 'future') {
        await messageApi.deleteFutureMessage(messageToDelete.id)
      } else if (type === 'waiting') {
        await messageApi.deleteQueueMessage(messageToDelete.id)
      } else {
        await messageApi.cancelMessage(messageToDelete.id)
      }
      showSuccess(t('notifications.operationSuccess'))
      fetchMessages()
      setShowDeleteConfirm(false)
      setMessageToDelete(null)
    } catch (err: any) {
      showError(err.response?.data?.message || t('notifications.operationError'))
    }
  }

  const handleToggleStatus = (message: Message) => {
    setMessageToToggle(message)
    setShowStatusConfirm(true)
  }

  const confirmToggleStatus = async () => {
    if (!messageToToggle) return

    try {
      // Only for waiting messages - use pause/resume API
      if (type === 'waiting') {
        const isPause = String(messageToToggle.status) === '1' // If processing (1), then pause it
        await messageApi.pauseMessage(messageToToggle.id, isPause)
      } else {
        // Fallback for other types
        const newStatus = String(messageToToggle.status) === '2' ? '1' : '2' // Resume or Pause
        await messageApi.updateMessageStatus(messageToToggle.id, newStatus)
      }
      showSuccess(t('notifications.operationSuccess'))
      fetchMessages()
      setShowStatusConfirm(false)
      setMessageToToggle(null)
    } catch (err: any) {
      showError(err.response?.data?.message || t('notifications.operationError'))
    }
  }

  const filterMessages = () => {
    let filtered = messages
    if (searchTerm) {
      filtered = filtered.filter(message =>
        message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.receiver.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    if (statusFilter) {
      filtered = filtered.filter(message => message.status === statusFilter)
    }
    if (dateFilter) {
      const filterDate = new Date(dateFilter)
      filtered = filtered.filter(message => {
        const messageDate = new Date(message.created_at)
        return messageDate.toDateString() === filterDate.toDateString()
      })
    }
    setFilteredMessages(filtered)
  }

  const getIcon = () => {
    switch (type) {
      case 'waiting': return Clock
      case 'future': return Calendar
      case 'sent': return CheckCircle
      default: return Clock
    }
  }
  const Icon = getIcon()

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-emerald-500 border-t-transparent"></div>
          <p className="text-gray-400 text-lg font-medium">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-6 py-8 space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-emerald-400">{title}</h1>
          <p className="text-gray-400 text-lg">{description}</p>
        </div>

        {/* Filters */}
        <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-2">{t('common.search')}</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                  placeholder={t('messages.searchPlaceholder') || 'Search message content or receiver...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-2">{t('messages.status')}</label>
              <select
                className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="" className="bg-gray-700">{t('messages.allStatuses') || 'All statuses'}</option>
                {Object.entries(statusMap).map(([status, info]) => (
                  <option key={status} value={status} className="bg-gray-700">{info.label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-2">{t('messages.date') || 'Date'}</label>
              <input
                type="date"
                className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
              />
            </div>
          </div>
        </div>



        {/* Messages List */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white flex items-center space-x-2">
                <Icon size={20} />
                <span>{title} ({filteredMessages.length})</span>
              </h3>
            </div>
          </div>
          <Table
            data={filteredMessages}
            columns={columns}
            loading={isLoading}
            emptyIcon={Icon}
            emptyTitle={t('messages.noMessages') || 'No messages found'}
            emptyDescription={
              searchTerm || statusFilter || dateFilter
                ? t('messages.noSearchResults') || 'No messages found matching your search criteria.'
                : t('messages.noMessagesInCategory') || 'No messages in this category yet.'
            }
            pageSize={10}
            showPagination={true}
          />
        </div>

        {/* Message Detail Modal */}
        {selectedMessage && (
          <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="relative w-full max-w-md mx-auto p-6 rounded-2xl bg-gray-800 shadow-xl">
              <h3 className="text-xl font-bold text-white mb-4">{t('messages.messageDetails') || 'Message Details'}</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-300">{t('messages.receiver')}:</label>
                  <p className="text-sm text-gray-200">{selectedMessage.receiver}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">{t('messages.content')}:</label>
                  <p className="text-sm text-gray-200 bg-gray-900 p-2 rounded">{selectedMessage.content}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">{t('messages.status')}:</label>
                  <span className={cn(
                    "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                    statusMap[selectedMessage.status as keyof typeof statusMap]?.color || 'bg-gray-500/20 text-gray-300'
                  )}>
                    {statusMap[selectedMessage.status as keyof typeof statusMap]?.label || t('messages.unknown') || 'Unknown'}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">{t('messages.sendTime')}:</label>
                  <p className="text-sm text-gray-200">{selectedMessage.send_time ? formatDate(selectedMessage.send_time) : '-'}</p>
                </div>
              </div>
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setSelectedMessage(null)}
                  className="px-4 py-2 rounded-lg bg-gray-700 text-gray-200 hover:bg-emerald-600 hover:text-white transition-all"
                >
                  {t('common.close')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDeleteMessage}
        title={t('messages.cancelMessage')}
        message={t('messages.cancelConfirm')}
        confirmText={t('common.delete')}
        type="danger"
      />

      {/* Status Toggle Confirmation Modal */}
      <ConfirmationModal
        isOpen={showStatusConfirm}
        onClose={() => setShowStatusConfirm(false)}
        onConfirm={confirmToggleStatus}
        title={String(messageToToggle?.status) === '2' ? t('messages.resumeMessage') : t('messages.pauseMessage')}
        message={String(messageToToggle?.status) === '2' ? t('messages.resumeConfirm') : t('messages.pauseConfirm')}
        confirmText={String(messageToToggle?.status) === '2' ? t('messages.resume') : t('messages.pause')}
        type="warning"
      />

      {/* Edit Future Message Modal */}
      {showEditModal && messageToEdit && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">{t('messages.editFutureMessage') || 'Edit Future Message'}</h3>
            <p className="text-gray-400 mb-6">
              {t('messages.editNotice') || 'For detailed editing, please use the Send Message page to create a new future message.'}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
