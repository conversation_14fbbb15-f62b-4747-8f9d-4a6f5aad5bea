import React, { useState, useEffect } from "react";
import {
  MessageSquare,
  Smartphone,
  Link,
  Plus,
  Minus,
  ShoppingCart,
  Trash2,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { cn } from "../../lib/utils";
import { useLanguage } from "../../contexts/LanguageContext";
import { currencyService } from "../../services/currencyService";

interface Package {
  id: string;
  type: "message" | "device" | "feature";
  name: string;
  amount: number;
  priceInTRY: number; // Base price in Turkish Lira
  unit: string;
  popular?: boolean;
  recommended?: boolean;
}

interface CartItem extends Package {
  quantity: number;
  displayPrice: number;
  currency: 'USD' | 'TRY';
}

const messagePackages: Package[] = [
  {
    id: "msg-100",
    type: "message",
    name: "100",
    amount: 100,
    priceInTRY: 50, // 100 mesaj = 50 TL (0.5 TL/mesaj)
    unit: "messages",
  },
  {
    id: "msg-300",
    type: "message",
    name: "300",
    amount: 300,
    priceInTRY: 135, // 300 mesaj = 135 TL (0.45 TL/mesaj)
    unit: "messages",
    popular: true,
  },
  {
    id: "msg-500",
    type: "message",
    name: "500",
    amount: 500,
    priceInTRY: 200, // 500 mesaj = 200 TL (0.4 TL/mesaj)
    unit: "messages",
  },
  {
    id: "msg-1000",
    type: "message",
    name: "1000",
    amount: 1000,
    priceInTRY: 400, // 1000 mesaj = 400 TL (0.4 TL/mesaj)
    unit: "messages",
    recommended: true,
  },
  {
    id: "msg-2000",
    type: "message",
    name: "2000",
    amount: 2000,
    priceInTRY: 700, // 2000 mesaj = 700 TL (0.35 TL/mesaj)
    unit: "messages",
  },
  {
    id: "msg-10000",
    type: "message",
    name: "10000",
    amount: 10000,
    priceInTRY: 3000, // 10000 mesaj = 3000 TL (0.3 TL/mesaj)
    unit: "messages",
  },
  {
    id: "msg-50000",
    type: "message",
    name: "50000",
    amount: 50000,
    priceInTRY: 12500, // 50000 mesaj = 12500 TL (0.25 TL/mesaj)
    unit: "messages",
  },
  {
    id: "msg-100000",
    type: "message",
    name: "100000",
    amount: 100000,
    priceInTRY: 20000, // 100000 mesaj = 20000 TL (0.2 TL/mesaj)
    unit: "messages",
  },
];

const devicePackages: Package[] = [
  {
    id: "dev-1",
    type: "device",
    name: "1",
    amount: 1,
    priceInTRY: 500, // Ekstra cihaz = 500 TL
    unit: "device",
  },
];

const featurePackages: Package[] = [
  {
    id: "feat-cancel-monthly",
    type: "feature",
    name: "cancelLink",
    amount: 1,
    priceInTRY: 100, // Cancel Link aylık = 100 TL
    unit: "month",
  },
  {
    id: "feat-cancel-yearly",
    type: "feature",
    name: "cancelLink",
    amount: 12,
    priceInTRY: 1000, // Cancel Link yıllık = 1000 TL
    unit: "year",
    recommended: true,
  },
];

export const Packages: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const currency = language === 'tr' ? 'TRY' : 'USD';

  // Convert price based on current language/currency
  const convertPrice = async (priceInTRY: number): Promise<number> => {
    if (currency === 'TRY') {
      return priceInTRY;
    }

    return await currencyService.convertPrice(priceInTRY, 'USD');
  };

  const addToCart = async (pkg: Package, quantity: number = 1) => {
    const displayPrice = await convertPrice(pkg.priceInTRY);

    setCart((prev) => {
      const existingItem = prev.find((item) => item.id === pkg.id);
      if (existingItem) {
        return prev.map((item) =>
          item.id === pkg.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }
      return [...prev, { ...pkg, quantity, displayPrice, currency }];
    });
  };

  const updateCartQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(id);
      return;
    }
    setCart((prev) =>
      prev.map((item) => (item.id === id ? { ...item, quantity } : item))
    );
  };

  const removeFromCart = (id: string) => {
    setCart((prev) => prev.filter((item) => item.id !== id));
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => total + item.displayPrice * item.quantity, 0);
  };

  const PackageCard: React.FC<{ pkg: Package }> = ({ pkg }) => {
    const [quantity, setQuantity] = useState(1);
    const [displayPrice, setDisplayPrice] = useState<number>(0);

    useEffect(() => {
      const updatePrice = async () => {
        const price = await convertPrice(pkg.priceInTRY);
        setDisplayPrice(price);
      };
      updatePrice();
    }, [pkg.priceInTRY, currency]);

    return (
      <div
        className={cn(
          "relative bg-gray-800 rounded-xl p-6 transition-all duration-200 hover:border-emerald-500/50",
          pkg.popular && "border-emerald-500 ring-2 ring-emerald-500/20",
          pkg.recommended && "border-blue-500 ring-2 ring-blue-500/20"
        )}
      >
        {pkg.popular && (
          <div className="absolute -top-3 -left-3 bg-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full transform -rotate-12 shadow-lg">
            {t("packages.mostPopular")}
          </div>
        )}
        {pkg.recommended && (
          <div className="absolute -top-3 -left-3 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full transform -rotate-12 shadow-lg">
            {t("packages.recommended")}
          </div>
        )}

        <div className="text-center">
          {pkg.type === "feature" && (
            <h2 className="text-lg font-semibold text-emerald-400 mb-2">
              {t(`packages.${pkg.name}`)}
            </h2>
          )}
          <h3 className="text-xl font-bold text-white mb-2">
            {pkg.type === "feature"
              ? `${pkg.amount} ${t(`packages.${pkg.unit}`)}`
              : `${pkg.amount} ${t(`packages.${pkg.unit}`)}`
            }
          </h3>
          <div className="text-3xl font-bold text-emerald-400 mb-2">
            {currencyService.formatPrice(displayPrice, currency)}
            <span className="text-sm text-gray-400 ml-1">
              {pkg.type === "feature"
                ? `/${t(
                    `packages.per${
                      pkg.unit.charAt(0).toUpperCase() + pkg.unit.slice(1)
                    }`
                  )}`
                : ""}
            </span>
          </div>

          {/* Per-unit price for message packages */}
          {pkg.type === "message" && (
            <div className="text-sm text-gray-400 mb-4">
              {currencyService.formatPrice(displayPrice / pkg.amount, currency)} {t("packages.perUnitPrice")}
            </div>
          )}

          <div className="flex items-center justify-center space-x-3 mb-4">
            <button
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <Minus size={16} />
            </button>
            <span className="text-white font-medium w-8 text-center">
              {quantity}
            </span>
            <button
              onClick={() => setQuantity(quantity + 1)}
              className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <Plus size={16} />
            </button>
          </div>

          <button
            onClick={() => addToCart(pkg, quantity)}
            className="w-full px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <ShoppingCart size={16} />
            <span>{t("packages.addToCart")}</span>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {t("packages.title")}
            </h1>
            <p className="text-gray-400">{t("packages.description")}</p>
          </div>

          <button
            onClick={() => setShowCart(true)}
            className="relative px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
          >
            <ShoppingCart size={16} />
            <span>{t("packages.cart")}</span>
            {cart.length > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {cart.reduce((total, item) => total + item.quantity, 0)}
              </span>
            )}
          </button>
        </div>

        {/* Message Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <MessageSquare className="h-6 w-6 text-emerald-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.messagePackages")}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {messagePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Device Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Smartphone className="h-6 w-6 text-blue-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.devicePackages")}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {devicePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Feature Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Link className="h-6 w-6 text-purple-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.featurePackages")}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featurePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Cart Modal */}
        {showCart && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">
                  {t("packages.cart")}
                </h3>
                <button
                  onClick={() => setShowCart(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <Plus className="rotate-45" size={20} />
                </button>
              </div>

              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart
                    size={48}
                    className="mx-auto text-gray-400 mb-4"
                  />
                  <p className="text-gray-400">{t("packages.cartEmpty")}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map((item) => (
                    <div
                      key={item.id}
                      className="bg-gray-700 rounded-lg p-4 flex items-center justify-between"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-white">
                          {item.amount} {t(`packages.${item.unit}`)}
                        </h4>
                        <p className="text-sm text-gray-400">
                          {currencyService.formatPrice(item.displayPrice, item.currency)} × {item.quantity} = {currencyService.formatPrice(item.displayPrice * item.quantity, item.currency)}
                        </p>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() =>
                              updateCartQuantity(item.id, item.quantity - 1)
                            }
                            className="w-6 h-6 bg-gray-600 hover:bg-gray-500 rounded-full flex items-center justify-center text-white transition-colors"
                          >
                            <Minus size={12} />
                          </button>
                          <span className="text-white font-medium w-8 text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() =>
                              updateCartQuantity(item.id, item.quantity + 1)
                            }
                            className="w-6 h-6 bg-gray-600 hover:bg-gray-500 rounded-full flex items-center justify-center text-white transition-colors"
                          >
                            <Plus size={12} />
                          </button>
                        </div>

                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}

                  <div className="border-t border-gray-600 pt-4">
                    <div className="flex items-center justify-between text-lg font-bold text-white mb-4">
                      <span>{t("packages.orderTotal")}</span>
                      <span>{currencyService.formatPrice(getTotalPrice(), currency)}</span>
                    </div>

                    <button
                      onClick={() => {
                        // Handle checkout
                        console.log("Checkout:", cart);
                        setShowCart(false);
                      }}
                      className="w-full px-4 py-3 bg-emerald-600 text-white rounded-lg font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    >
                      {t("packages.checkout")}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
