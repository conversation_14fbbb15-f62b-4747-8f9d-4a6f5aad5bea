import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { authApi, user<PERSON>pi, LoginRequest, LoginResponse } from '../lib/api'

interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  country_code: string
  language: string
  avatar?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  updateUser: (userData: User) => void
  isLoading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const storedToken = localStorage.getItem('auth_token')
    const storedUser = localStorage.getItem('user')

    console.log('AuthContext: Checking stored auth data', { storedToken: !!storedToken, storedUser: !!storedUser })

    if (storedToken && storedUser) {
      try {
        setToken(storedToken)
        setUser(JSON.parse(storedUser))
        console.log('AuthContext: Successfully restored auth state')
      } catch (error) {
        console.error('AuthContext: Error parsing stored user data', error)
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user')
      }
    }

    setIsLoading(false)
  }, [])

  const login = async (credentials: LoginRequest) => {
    try {
      console.log('AuthContext: Attempting login with', { identify: credentials.identify })
      const response: LoginResponse = await authApi.login(credentials)
      console.log('AuthContext: Login response', { isSucceeded: response.IsSucceeded })

      if (response.IsSucceeded) {
        // Store token
        setToken(response.Token)
        localStorage.setItem('auth_token', response.Token)
        localStorage.setItem('token_expires', response.Expires)
        console.log('AuthContext: Token stored successfully')

        // Fetch user details from API
        try {
          const userData = await userApi.getCurrentUser()
          setUser(userData)
          localStorage.setItem('user', JSON.stringify(userData))
          console.log('AuthContext: User data fetched and stored')
        } catch (userError) {
          console.error('Error fetching user data:', userError)
          // Fallback to minimal user object if API call fails
          const tempUser: User = {
            id: 'temp-id',
            first_name: 'User',
            last_name: '',
            email: credentials.identify.includes('@') ? credentials.identify : '',
            phone: !credentials.identify.includes('@') ? credentials.identify : '',
            country_code: '',
            language: 'tr'
          }
          setUser(tempUser)
          localStorage.setItem('user', JSON.stringify(tempUser))
          console.log('AuthContext: Using fallback user data')
        }
      } else {
        throw new Error('Login failed')
      }
    } catch (error) {
      console.error('AuthContext: Login error', error)
      throw error
    }
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
  }

  const updateUser = (userData: User) => {
    setUser(userData)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    updateUser,
    isLoading,
    isAuthenticated: !!token,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
