import { useState } from 'react'
import { Outlet } from 'react-router-dom'
import Sidebar from '../components/Sidebar'
import { ProtectedRoute } from '../components/ProtectedRoute'
import { NotificationContainer } from '../components/NotificationContainer'

export function UserLayout() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Main Content */}
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-900">
            <Outlet />
          </main>
        </div>

        {/* Notification Container */}
        <NotificationContainer />
      </div>
    </ProtectedRoute>
  )
}
