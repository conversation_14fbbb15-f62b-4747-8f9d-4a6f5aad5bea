{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "add": "Add", "adding": "Adding...", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "online": "Online", "offline": "Offline", "optional": "Optional", "test": "Test", "required": "is required", "confirmMessage": "Are you sure you want to proceed with this action?"}, "table": {"showing": "Showing", "of": "of", "results": "results", "noData": "No data available", "sortBy": "Sort by"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone", "countryCode": "Country Code", "rememberMe": "Remember me", "forgotPassword": "Forgot password", "loginTitle": "WhatsApp Panel", "loginSubtitle": "Sign in to your account", "registerTitle": "Register", "registerSubtitle": "Create a new account", "loggingIn": "Logging in...", "registering": "Creating account...", "loginError": "An error occurred while logging in", "registerError": "An error occurred while registering", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "passwordMismatch": "Passwords do not match", "language": "Language", "selectLanguage": "Select Language", "registerSuccess": "Registration successful! You can now login.", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordPlaceholder": "Re-enter your password", "phonePlaceholder": "555 123 45 67", "termsAccept": "I accept the ", "privacyPolicy": "privacy policy", "termsAcceptEnd": " and terms of service", "or": "or", "google": "Google", "twitter": "Twitter", "defaultUser": "User", "defaultEmail": "<EMAIL>"}, "navigation": {"dashboard": "Dashboard", "sendMessage": "Send Message", "blacklist": "Blacklist", "waitingMessages": "Waiting Messages", "futureMessages": "Future Messages", "sentMessages": "Sent Messages", "devices": "Devices", "groups": "Groups", "packages": "Packages", "settings": "Settings"}, "dashboard": {"title": "Dashboard", "subtitle": "Real-time messaging statistics and performance analysis", "live": "Live", "timeRange": "Time Range", "messageStatus": "Message Status", "all": "All", "totalMessages": "Total Messages", "successfulMessages": "Successful Messages", "failedMessages": "Failed Messages", "pendingMessages": "Pending Messages", "successRate": "Success Rate", "messageStats": "Message Statistics", "recentActivity": "Recent Activity", "filterByDay": "Daily", "filterByMonth": "Monthly", "filterByYear": "Yearly", "success": "Success", "failed": "Failed", "monthlyTrend": "Monthly Message Trend", "successDistribution": "Success Distribution", "dailyFlow": "Daily Message Flow", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June"}, "loadError": "Error loading report data", "thisMonth": "this month"}, "messages": {"send": "Send", "content": "Message Content", "receiver": "Receiver", "receivers": "Receivers", "sendTime": "Send Time", "status": "Status", "sent": "<PERSON><PERSON>", "pending": "Pending", "failed": "Failed", "processing": "Processing", "paused": "Paused", "future": "Future", "canceled": "Canceled", "enterMessage": "Enter your message...", "enterReceivers": "Enter receiver numbers separated by commas", "selectDateTime": "Select date and time", "sendNow": "Send Now", "scheduleMessage": "Schedule Message", "messagesSent": "Message sent", "messagesScheduled": "Message scheduled", "sendDescription": "Send bulk messages via WhatsApp", "messageType": "Message Type", "text": "Text", "media": "Media", "poll": "Poll", "deviceSelection": "Device Selection", "selectDevice": "Select device...", "noDevices": "No connected devices found. Please connect a device first.", "groupSelection": "Group Selection", "selectGroup": "Select group...", "selectGroupToAddContacts": "Select a group to add all its contacts to receivers list", "contactsAdded": "contacts added from group", "allContactsAlreadyAdded": "All contacts from this group are already added", "allGroupsUsed": "All available groups have been used", "noGroupsWithContacts": "No groups with contacts available", "people": "people", "phoneNumberHint": "Enter phone numbers with country code", "addReceiver": "Add Receiver", "addedReceivers": "Added Receivers", "noReceiversAdded": "No receivers added yet", "removeReceiver": "Remove Receiver", "editReceiver": "Edit Receiver", "phoneNumberRequired": "Phone number is required", "invalidPhoneNumber": "Invalid phone number", "receiverExists": "This number is already added", "sendTimeHint": "If left empty, message will be sent immediately", "sending": "Sending...", "totalGroups": "Total Groups", "activeDevices": "Active Devices", "sentToday": "Sent Today", "scheduled": "Scheduled", "sendError": "Error sending message", "insufficientCredit": "Insufficient credit. You have 0 credit remaining.", "creditExhausted": "Credit Exhausted", "purchaseMessagePackage": "You can purchase a message package to continue sending messages.", "goToPackages": "Go to Packages", "validation": {"receiversRequired": "Please enter at least one receiver number", "deviceRequired": "Please select a device"}, "loadError": "Error loading messages", "searchPlaceholder": "Search message content or receiver...", "allStatuses": "All statuses", "date": "Date", "noMessages": "No messages found", "noSearchResults": "No messages found matching your search criteria.", "noMessagesInCategory": "No messages in this category yet.", "createdAt": "Created", "actions": "Actions", "unknown": "Unknown", "viewDetails": "View details", "cancel": "Cancel", "delete": "Delete", "messageDetails": "Message Details", "waitingDescription": "View messages that are being processed and paused", "futureDescription": "Manage scheduled and future messages", "sentDescription": "View successful and failed message deliveries", "pause": "Pause", "resume": "Resume", "pauseMessage": "Pause Message", "resumeMessage": "Resume Message", "pauseConfirm": "Are you sure you want to pause this message?", "resumeConfirm": "Are you sure you want to resume this message?", "cancelMessage": "Cancel Message", "cancelConfirm": "Are you sure you want to cancel this message? This action cannot be undone.", "fileUpload": "File Upload", "selectFile": "Select File", "fileRequired": "Please select a file", "pollOptions": "Poll Options", "addPollOption": "Add Poll Option", "pollOption": "Poll Option", "selectableCount": "Selectable Options Count", "selectableCountPlaceholder": "How many options can be selected?", "pollOptionPlaceholder": "Enter poll option...", "removePollOption": "Remove option", "pollOptionsRequired": "Please add at least 2 poll options", "supportedFormats": "Images, videos, audio, documents", "edit": "Edit", "editFutureMessage": "Edit Future Message", "editNotice": "For detailed editing, please use the Send Message page to create a new future message."}, "blacklist": {"description": "Manage phone numbers that will not receive messages", "addToBlacklist": "Add to Blacklist", "reason": "Reason", "phonePlaceholder": "905551234567", "reasonPlaceholder": "Reason for blacklisting", "adding": "Adding...", "addSuccess": "Number added to blacklist!", "addError": "Error adding to blacklist", "removeConfirm": "Are you sure you want to remove this number from blacklist?", "searchPlaceholder": "Search phone number or reason...", "emptyTitle": "Blacklist is empty", "emptyDescription": "No numbers have been added to the blacklist yet.", "noSearchResults": "No results found for your search criteria.", "dateAdded": "Date Added", "actions": "Actions", "removeFromBlacklist": "Remove from blacklist", "totalBlacklisted": "Total Blacklisted", "addedThisMonth": "Added This Month", "removedThisMonth": "Removed This Month", "removeSuccess": "Number removed from blacklist!", "removeError": "Error removing from blacklist", "validation": {"phoneRequired": "Phone number is required"}}, "sidebar": {"appName": "WhatsApp Pro", "appSubtitle": "Message Panel", "settings": "Settings", "logout": "Logout", "systemActive": "System Active"}, "devices": {"description": "Manage your WhatsApp connections", "loadError": "Error loading devices", "qrError": "Error generating QR code", "logoutConfirm": "Are you sure you want to force logout this device?", "logoutSuccess": "<PERSON><PERSON> logged out successfully!", "logoutError": "Error logging out device", "generating": "Generating...", "addDevice": "Add New Device", "online": "Online", "offline": "Offline", "registrationId": "Registration ID", "connectedDevices": "Connected Devices", "noDevices": "No devices found", "noDevicesDescription": "No connected devices yet. Generate QR code to add new device.", "device": "<PERSON><PERSON>", "lastSeen": "Last Seen", "regId": "Reg ID", "whatsappDevice": "WhatsApp Device", "logoutDevice": "Logout device", "activeDevices": "Active Devices", "inactiveDevices": "Inactive Devices", "totalDevices": "Total Devices", "qrTitle": "WhatsApp QR Code", "qrInstruction": "Scan this QR code with your WhatsApp app", "addDeviceTitle": "Add New Device", "addDeviceDescription": "Choose how you want to connect your WhatsApp device", "addWithQr": "Add with QR Code", "addWithCode": "Add with Code", "qrMethod": "QR Code Method", "qrMethodDescription": "Scan QR code with your WhatsApp app", "codeMethod": "Code Method", "codeMethodDescription": "Enter your phone number to receive a code", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "getCode": "Get Code", "enterCode": "Enter Code", "codeInstruction": "Enter the code sent to your phone", "verifyCode": "Verify Code", "gettingCode": "Getting code...", "verifying": "Verifying...", "codeError": "Error generating code", "deviceLimitExceeded": "Device limit exceeded", "deviceLimitMessage": "You need to purchase a device package to add more devices.", "buyPackages": "Buy Packages", "goToPackages": "Go to Packages", "qrExpired": "QR Code Expired", "qrExpiredMessage": "The QR code has expired. Please generate a new one.", "generateNewQr": "Generate New QR", "generatingQr": "Generating...", "expiresIn": "Expires in", "codeExpired": "Code Expired", "codeExpiredMessage": "The verification code has expired. Please request a new one.", "getNewCode": "Get New Code", "codeDisplayInstruction": "Enter this code in your WhatsApp mobile app", "verificationCode": "Verification Code", "enterCodeInWhatsApp": "Enter this code in WhatsApp", "codeInstructions": "Open WhatsApp on your phone → Settings → Linked Devices → Link a Device → Enter the code above", "deviceConnected": "<PERSON>ce connected successfully!", "fullPhoneExample": "Full number example"}, "groups": {"description": "Create and manage your contact groups", "loadError": "Error loading groups", "nameRequired": "Group name is required", "createSuccess": "Group created successfully!", "createError": "Error creating group", "deleteConfirm": "Are you sure you want to delete this group?", "deleteSuccess": "Group deleted successfully!", "deleteError": "Error deleting group", "createNew": "Create New Group", "contactsLoadError": "Error loading group contacts", "searchPlaceholder": "Search group name or description...", "noGroups": "No groups found", "noSearchResults": "No groups found matching your search criteria.", "noGroupsCreated": "No groups created yet.", "people": "people", "viewContacts": "View contacts", "totalGroups": "Total Groups", "totalContacts": "Total Contacts", "createdThisMonth": "Created This Month", "groupName": "Group Name", "groupNamePlaceholder": "Enter group name", "descriptionPlaceholder": "Group description (optional)", "creating": "Creating...", "create": "Create", "contacts": "Contacts", "noContacts": "No contacts found", "noContactsInGroup": "No contacts in this group yet.", "fullName": "Full Name", "addContact": "Add Contact", "addNewContact": "Add New Contact", "firstName": "First Name", "lastName": "Last Name", "fillAllFields": "Please fill all fields", "deleteContactConfirm": "Are you sure you want to delete this contact?", "deleteGroup": "Delete Group", "removeContact": "Remove Contact"}, "settings": {"title": "Settings", "description": "Manage your account settings and preferences", "personalInfo": "Personal Information", "accountSettings": "Account <PERSON><PERSON>", "preferences": "Preferences", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "language": "Language", "avatar": "Profile Picture", "changeAvatar": "Change Avatar", "removeAvatar": "Remove Avatar", "uploadAvatar": "Upload Avatar", "selectImage": "Select Image", "dragDropImage": "Drag and drop an image here, or click to select", "supportedFormats": "Supported formats: JPG, PNG, GIF (max 5MB)", "updating": "Updating...", "updateSuccess": "Settings updated successfully!", "updateError": "Error updating settings", "avatarUpdateSuccess": "Avatar updated successfully!", "avatarUpdateError": "Error updating avatar", "avatarRemoveSuccess": "Avatar removed successfully!", "avatarRemoveError": "Error removing avatar", "languageUpdateSuccess": "Language updated successfully!", "languageUpdateError": "Error updating language", "selectLanguage": "Select Language", "updateLanguage": "Update Language", "english": "English", "turkish": "Turkish", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "fileTooLarge": "File size must be less than 5MB", "invalidFileType": "Please select a valid image file (JPG, PNG, GIF)"}}, "packages": {"title": "Packages", "description": "Choose the package that suits your needs", "messagePackages": "Message Packages", "devicePackages": "Device Packages", "featurePackages": "Feature Packages", "messages": "Messages", "device": "<PERSON><PERSON>", "devices": "Devices", "month": "Month", "year": "Year", "cancelLink": "Cancel Link", "monthly": "Monthly", "yearly": "Yearly", "addToCart": "Add to Cart", "buyNow": "Buy Now", "quantity": "Quantity", "total": "Total", "price": "Price", "perMessage": "per message", "perDevice": "per device", "perMonth": "per month", "perYear": "per year", "save": "Save", "mostPopular": "Most Popular", "recommended": "Recommended", "cart": "<PERSON><PERSON>", "cartEmpty": "Your cart is empty", "removeFromCart": "Remove from cart", "checkout": "Checkout", "orderTotal": "Order Total", "perUnitPrice": "per unit"}, "notifications": {"loginSuccess": "Login successful!", "loginError": "<PERSON><PERSON> failed. Please check your credentials.", "registerSuccess": "Registration successful! You can now login.", "registerError": "Registration failed. Please try again.", "logoutSuccess": "Logged out successfully!", "updateSuccess": "Updated successfully!", "updateError": "Update failed. Please try again.", "deleteSuccess": "Deleted successfully!", "deleteError": "Delete failed. Please try again.", "saveSuccess": "Saved successfully!", "saveError": "Save failed. Please try again.", "loadError": "Failed to load data. Please try again.", "networkError": "Network error. Please check your connection.", "unexpectedError": "An unexpected error occurred.", "operationSuccess": "Operation completed successfully!", "operationError": "Operation failed. Please try again.", "validationError": "Please check your input and try again.", "permissionError": "You don't have permission to perform this action.", "sessionExpired": "Your session has expired. Please login again.", "fileUploadSuccess": "File uploaded successfully!", "fileUploadError": "File upload failed. Please try again.", "connectionSuccess": "Connected successfully!", "connectionError": "Connection failed. Please try again.", "disconnectionSuccess": "Disconnected successfully!", "disconnectionError": "Disconnection failed. Please try again."}}