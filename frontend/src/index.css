@import "tailwindcss";

/* Base styles */
* {
  border-color: rgb(229 231 235);
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

body {
  background-color: rgb(17 24 39);
  color: rgb(17 24 39);
  font-family: system-ui, -apple-system, sans-serif;
}

#root {
  height: 100%;
  width: 100%;
}

/* Component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border: none;
}

.btn:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(17 24 39);
}

.btn-secondary:hover {
  background-color: rgb(209 213 219);
}

.btn-success {
  background-color: rgb(34 197 94);
  color: white;
}

.btn-success:hover {
  background-color: rgb(22 163 74);
}

.btn-danger {
  background-color: rgb(239 68 68);
  color: white;
}

.btn-danger:hover {
  background-color: rgb(220 38 38);
}

.card {
  border-radius: 0.5rem;
  border: 1px solid rgb(229 231 235);
  background-color: white;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid rgb(209 213 219);
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.input::placeholder {
  color: rgb(107 114 128);
}

.input:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
