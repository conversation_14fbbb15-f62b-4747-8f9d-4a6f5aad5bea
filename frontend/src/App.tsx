import './App.css'
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { userRoutes } from './routes/user.routes';
import { mainRoutes } from './routes/main.routes';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import { RouteType } from './routes/routes.types';

const renderRoutes = (route: RouteType) => (
  <Route key={route.path} path={route.path} element={route.element}>
    {route.children?.map((child) => renderRoutes(child))}
  </Route>
);

function App() {
  return (
    <AuthProvider>
      <LanguageProvider>
        <NotificationProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              {renderRoutes(userRoutes)}
              {renderRoutes(mainRoutes)}
            </Routes>
          </BrowserRouter>
        </NotificationProvider>
      </LanguageProvider>
    </AuthProvider>
  )
}

export default App
