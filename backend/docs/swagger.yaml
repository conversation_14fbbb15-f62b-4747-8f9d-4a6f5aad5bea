basePath: /api/v1
definitions:
  dtos.AddContact:
    properties:
      first_name:
        type: string
      group_id:
        type: string
      last_name:
        type: string
      country_code:
        type: string
      number:
        type: string
    type: object
  dtos.BlackList:
    properties:
      device_number:
        type: string
      country_code:
        type: string
      number:
        type: string
    type: object
  dtos.ChatMessagesReq:
    properties:
      chat_id:
        type: string
      reg_id:
        type: string
    type: object
  dtos.ChatMessagesRes:
    properties:
      from:
        type: string
      message:
        type: string
      send_time:
        type: string
    type: object
  dtos.ChatRes:
    properties:
      chat_id:
        type: string
      message:
        type: string
      send_time:
        type: string
    type: object
  dtos.CheckActiveDeviceData:
    properties:
      device_number:
        type: string
      j_id:
        type: string
      platform:
        type: string
      user_name:
        type: string
    type: object
  dtos.CheckActiveDeviceRes:
    properties:
      data:
        $ref: '#/definitions/dtos.CheckActiveDeviceData'
      is_connected:
        type: boolean
      message:
        type: string
    type: object
  dtos.CheckDeviceRes:
    properties:
      is_connected:
        type: boolean
      message:
        type: string
    type: object
  dtos.CodeResponse:
    properties:
      code:
        type: string
      regId:
        type: string
    type: object
  dtos.ConnectWpFailRes:
    properties:
      device_limit_exceed:
        type: boolean
      error:
        type: string
    type: object
  dtos.ConnectWpReq:
    properties:
      phone:
        type: string
    type: object
  dtos.Contact:
    properties:
      first_name:
        type: string
      last_name:
        type: string
      number:
        type: string
    type: object
  dtos.CreateUserReq:
    properties:
      country_code:
        type: string
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        type: string
      phone:
        type: string
    type: object
  dtos.Device:
    properties:
      business_name:
        type: string
      device_number:
        type: string
      j_id:
        type: string
      platform:
        type: string
      push_name:
        type: string
      registration_id:
        type: string
      state:
        description: 1- online 2- çıkış
        type: string
    type: object
  dtos.Group:
    properties:
      description:
        type: string
      name:
        type: string
    type: object
  dtos.LoginRequest:
    properties:
      identify:
        type: string
      password:
        type: string
    required:
    - identify
    - password
    type: object
  dtos.LoginResponse:
    properties:
      Expires:
        type: string
      IsSucceeded:
        type: boolean
      Token:
        type: string
    type: object
  dtos.MessageCallBack:
    properties:
      callback_data:
        type: string
      content:
        type: string
      phone:
        type: string
      send_time:
        type: string
      status:
        description: '1: Sended 2: Not Sended'
        type: string
    type: object
  dtos.MessageCallBackArray:
    properties:
      messages:
        items:
          $ref: '#/definitions/dtos.MessageCallBack'
        type: array
    type: object
  dtos.QrResponse:
    properties:
      qr:
        type: string
      regId:
        type: string
    type: object
  dtos.Report:
    properties:
      failed:
        type: integer
      successed:
        type: integer
    type: object
  dtos.SendMessageDto:
    properties:
      content:
        type: string
      format:
        type: string
      poll_option:
        items:
          type: string
        type: array
      receivers:
        items:
          type: string
        type: array
      reg_id:
        type: string
      reply_option:
        items:
          type: string
        type: array
      selectable_option_count:
        type: integer
      send_time:
        type: string
      user_id:
        type: string
    type: object
  dtos.UpdateContact:
    properties:
      contact_id:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      number:
        type: string
    type: object
  dtos.UserRes:
    properties:
      country_code:
        type: string
      first_name:
        type: string
      id:
        type: string
      last_name:
        type: string
      phone:
        type: string
    type: object
  dtos.UserUpdate:
    properties:
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        type: string
    type: object
  dtos.WhatsappLogoutReq:
    properties:
      reg_id:
        type: string
    type: object
  entities.Message:
    properties:
      content:
        example: Merhaba
        type: string
      created_at:
        example: "2021-01-01T00:00:00Z"
        type: string
      deleted_at:
        format: date-time
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_sent:
        default: false
        example: true
        type: boolean
      receiver:
        description: phone numbers must be separated by commas
        example: 90555555555,905555555556
        type: string
      reg_id:
        example: "1234567"
        type: string
      report_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      send_time:
        example: "2006-01-02 15:04:05"
        type: string
      status:
        description: 1-processing, 2-paused, 3-successed, 4-failed, 5-future message
          6-canceled
        type: string
      updated_at:
        example: "2021-01-01T00:00:00Z"
        type: string
      user_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    type: object
info:
  contact: {}
  description: Whatsapp API Documentation
  title: Whatsapp API
  version: "1.0"
paths:
  /blacklist:
    post:
      consumes:
      - application/json
      description: This endpoint adds a number to the blacklist.
      parameters:
      - description: Blacklist Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.BlackList'
      produces:
      - application/json
      responses:
        "200":
          description: success message
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Add number to blacklist
      tags:
      - Blacklist
  /device/check:
    get:
      consumes:
      - application/json
      description: This endpoint checks if a device is registered.
      parameters:
      - description: Registration ID
        in: query
        name: reg_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: device check response
          schema:
            $ref: '#/definitions/dtos.CheckDeviceRes'
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check device
      tags:
      - Device
  /device/check-active:
    get:
      consumes:
      - application/json
      description: This endpoint checks if a device is active.
      parameters:
      - description: Registration ID
        in: query
        name: reg_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: active device check response
          schema:
            $ref: '#/definitions/dtos.CheckActiveDeviceRes'
        "404":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check active device
      tags:
      - Device
  /device/code:
    post:
      consumes:
      - application/json
      description: This endpoint retrieves a code for the user.
      parameters:
      - description: Connect WP Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.ConnectWpReq'
      produces:
      - application/json
      responses:
        "200":
          description: code response
          schema:
            $ref: '#/definitions/dtos.CodeResponse'
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get code
      tags:
      - Device
  /device/list:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of devices for the user.
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Items per page
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: list of devices
          schema:
            items:
              $ref: '#/definitions/dtos.Device'
            type: array
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get devices
      tags:
      - Device
  /device/logout:
    post:
      consumes:
      - application/json
      description: This endpoint logs out the user from a device.
      parameters:
      - description: Logout Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.WhatsappLogoutReq'
      produces:
      - application/json
      responses:
        "200":
          description: success message
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Logout from device
      tags:
      - Device
  /device/qr:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a QR code for the user.
      produces:
      - application/json
      responses:
        "200":
          description: QR code response
          schema:
            $ref: '#/definitions/dtos.QrResponse'
        "404":
          description: error message
          schema:
            $ref: '#/definitions/dtos.ConnectWpFailRes'
      summary: Get QR code
      tags:
      - Device
  /group:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of groups for the user.
      produces:
      - application/json
      responses:
        "200":
          description: list of groups
          schema:
            items:
              $ref: '#/definitions/dtos.Group'
            type: array
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get groups
      tags:
      - Group
    post:
      consumes:
      - application/json
      description: This endpoint creates a new group.
      parameters:
      - description: Group Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.Group'
      produces:
      - application/json
      responses:
        "201":
          description: Group created successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create group
      tags:
      - Group
  /group/{id}:
    delete:
      consumes:
      - application/json
      description: This endpoint deletes a group.
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Group deleted successfully
          schema:
            type: string
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete group
      tags:
      - Group
    put:
      consumes:
      - application/json
      description: This endpoint updates a group.
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: string
      - description: Group Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.Group'
      produces:
      - application/json
      responses:
        "200":
          description: Group updated successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update group
      tags:
      - Group
  /group/{id}/contacts:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of contacts in a group.
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: list of contacts
          schema:
            items:
              $ref: '#/definitions/dtos.Contact'
            type: array
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: List contacts
      tags:
      - Group
  /group/contact:
    post:
      consumes:
      - application/json
      description: This endpoint adds a contact to a group.
      parameters:
      - description: Add Contact Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.AddContact'
      produces:
      - application/json
      responses:
        "201":
          description: Contact added successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Add contact
      tags:
      - Group
  /group/contact/{contactId}:
    delete:
      consumes:
      - application/json
      description: This endpoint deletes a contact from a group.
      parameters:
      - description: Contact ID
        in: path
        name: contactId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Contact deleted successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete contact
      tags:
      - Group
    put:
      consumes:
      - application/json
      description: This endpoint updates a contact in a group.
      parameters:
      - description: Contact ID
        in: path
        name: contactId
        required: true
        type: string
      - description: Update Contact Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.UpdateContact'
      produces:
      - application/json
      responses:
        "200":
          description: Contact updated successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update contact
      tags:
      - Group
  /message/callback:
    post:
      consumes:
      - application/json
      description: This endpoint handles the WhatsApp callback.
      parameters:
      - description: Message Callback Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.MessageCallBackArray'
      produces:
      - application/json
      responses:
        "200":
          description: Callback handled successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: WhatsApp callback
      tags:
      - Message
  /message/chat-messages:
    post:
      consumes:
      - application/json
      description: This endpoint retrieves messages from a chat.
      parameters:
      - description: Chat Messages Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.ChatMessagesReq'
      produces:
      - application/json
      responses:
        "200":
          description: list of messages
          schema:
            items:
              $ref: '#/definitions/dtos.ChatMessagesRes'
            type: array
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get chat messages
      tags:
      - Message
  /message/chats:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of chats.
      parameters:
      - description: Registration ID
        in: query
        name: reg_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: list of chats
          schema:
            items:
              $ref: '#/definitions/dtos.ChatRes'
            type: array
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get chats
      tags:
      - Message
  /message/messages:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of messages.
      parameters:
      - description: Message status
        in: query
        name: status
        type: string
      - description: Page number
        in: query
        name: page
        type: string
      - description: Items per page
        in: query
        name: per_page
        type: string
      - description: Initial time
        in: query
        name: initial_time
        type: string
      - description: End time
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: list of messages
          schema:
            items:
              $ref: '#/definitions/entities.Message'
            type: array
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get messages
      tags:
      - Message
  /message/reports:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of reports.
      produces:
      - application/json
      responses:
        "200":
          description: list of reports
          schema:
            items:
              $ref: '#/definitions/dtos.Report'
            type: array
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get reports
      tags:
      - Message
  /message/send:
    post:
      consumes:
      - application/json
      description: This endpoint sends a message.
      parameters:
      - description: Send Message Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.SendMessageDto'
      produces:
      - application/json
      responses:
        "200":
          description: Message sent successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Send message
      tags:
      - Message
  /user:
    delete:
      consumes:
      - application/json
      description: This endpoint deletes a user.
      produces:
      - application/json
      responses:
        "200":
          description: User deleted successfully
          schema:
            type: string
        "404":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete user
      tags:
      - User
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a user by their ID.
      produces:
      - application/json
      responses:
        "200":
          description: User details
          schema:
            $ref: '#/definitions/dtos.UserRes'
        "404":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get user by ID
      tags:
      - User
    post:
      consumes:
      - application/json
      description: This endpoint creates a new user.
      parameters:
      - description: Create User Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.CreateUserReq'
      produces:
      - application/json
      responses:
        "201":
          description: User created
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create user
      tags:
      - User
    put:
      consumes:
      - application/json
      description: This endpoint updates a user's details.
      parameters:
      - description: Update User Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.UserUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            type: string
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update user
      tags:
      - User
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
