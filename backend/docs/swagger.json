{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Whatsapp API Documentation", "title": "Whatsapp API", "contact": {}, "version": "1.0"}, "basePath": "/api/v1", "paths": {"/blacklist": {"post": {"description": "This endpoint adds a number to the blacklist.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Blacklist"], "summary": "Add number to blacklist", "parameters": [{"description": "Blacklist Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.BlackList"}}], "responses": {"200": {"description": "success message", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/check": {"get": {"description": "This endpoint checks if a device is registered.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Check device", "parameters": [{"type": "string", "description": "Registration ID", "name": "reg_id", "in": "query", "required": true}], "responses": {"200": {"description": "device check response", "schema": {"$ref": "#/definitions/dtos.CheckDeviceRes"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/check-active": {"get": {"description": "This endpoint checks if a device is active.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Check active device", "parameters": [{"type": "string", "description": "Registration ID", "name": "reg_id", "in": "query", "required": true}], "responses": {"200": {"description": "active device check response", "schema": {"$ref": "#/definitions/dtos.CheckActiveDeviceRes"}}, "404": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/code": {"post": {"description": "This endpoint retrieves a code for the user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Get code", "parameters": [{"description": "Connect WP Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.ConnectWpReq"}}], "responses": {"200": {"description": "code response", "schema": {"$ref": "#/definitions/dtos.CodeResponse"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/list": {"get": {"description": "This endpoint retrieves a list of devices for the user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Get devices", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page", "name": "per_page", "in": "query"}], "responses": {"200": {"description": "list of devices", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.Device"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/logout": {"post": {"description": "This endpoint logs out the user from a device.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Logout from device", "parameters": [{"description": "Logout Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.WhatsappLogoutReq"}}], "responses": {"200": {"description": "success message", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/qr": {"get": {"description": "This endpoint retrieves a QR code for the user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Get QR code", "responses": {"200": {"description": "QR code response", "schema": {"$ref": "#/definitions/dtos.QrResponse"}}, "404": {"description": "error message", "schema": {"$ref": "#/definitions/dtos.ConnectWpFailRes"}}}}}, "/group": {"get": {"description": "This endpoint retrieves a list of groups for the user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Get groups", "responses": {"200": {"description": "list of groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.Group"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "This endpoint creates a new group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Create group", "parameters": [{"description": "Group Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.Group"}}], "responses": {"201": {"description": "Group created successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/group/contact": {"post": {"description": "This endpoint adds a contact to a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Add contact", "parameters": [{"description": "Add Contact Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.AddContact"}}], "responses": {"201": {"description": "Contact added successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/group/contact/{contactId}": {"put": {"description": "This endpoint updates a contact in a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Update contact", "parameters": [{"type": "string", "description": "Contact ID", "name": "contactId", "in": "path", "required": true}, {"description": "Update Contact Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.UpdateContact"}}], "responses": {"200": {"description": "Contact updated successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "This endpoint deletes a contact from a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Delete contact", "parameters": [{"type": "string", "description": "Contact ID", "name": "contactId", "in": "path", "required": true}], "responses": {"200": {"description": "Contact deleted successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/group/{id}": {"put": {"description": "This endpoint updates a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Update group", "parameters": [{"type": "string", "description": "Group ID", "name": "id", "in": "path", "required": true}, {"description": "Group Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.Group"}}], "responses": {"200": {"description": "Group updated successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "This endpoint deletes a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "Delete group", "parameters": [{"type": "string", "description": "Group ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Group deleted successfully", "schema": {"type": "string"}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/group/{id}/contacts": {"get": {"description": "This endpoint retrieves a list of contacts in a group.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Group"], "summary": "List contacts", "parameters": [{"type": "string", "description": "Group ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "list of contacts", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.Contact"}}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/callback": {"post": {"description": "This endpoint handles the WhatsApp callback.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "WhatsApp callback", "parameters": [{"description": "Message Callback Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.MessageCallBackArray"}}], "responses": {"200": {"description": "Callback handled successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/chat-messages": {"post": {"description": "This endpoint retrieves messages from a chat.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get chat messages", "parameters": [{"description": "Chat Messages Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.ChatMessagesReq"}}], "responses": {"200": {"description": "list of messages", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.ChatMessagesRes"}}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/chats": {"get": {"description": "This endpoint retrieves a list of chats.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get chats", "parameters": [{"type": "string", "description": "Registration ID", "name": "reg_id", "in": "query", "required": true}], "responses": {"200": {"description": "list of chats", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.ChatRes"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/messages": {"get": {"description": "This endpoint retrieves a list of messages.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get messages", "parameters": [{"type": "string", "description": "Message status", "name": "status", "in": "query"}, {"type": "string", "description": "Page number", "name": "page", "in": "query"}, {"type": "string", "description": "Items per page", "name": "per_page", "in": "query"}, {"type": "string", "description": "Initial time", "name": "initial_time", "in": "query"}, {"type": "string", "description": "End time", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "list of messages", "schema": {"type": "array", "items": {"$ref": "#/definitions/entities.Message"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/reports": {"get": {"description": "This endpoint retrieves a list of reports.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get reports", "responses": {"200": {"description": "list of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.Report"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/send": {"post": {"description": "This endpoint sends a message.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Send message", "parameters": [{"description": "Send Message Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.SendMessageDto"}}], "responses": {"200": {"description": "Message sent successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/user": {"get": {"description": "This endpoint retrieves a user by their ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Get user by ID", "responses": {"200": {"description": "User details", "schema": {"$ref": "#/definitions/dtos.UserRes"}}, "404": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"description": "This endpoint updates a user's details.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Update user", "parameters": [{"description": "Update User Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.UserUpdate"}}], "responses": {"200": {"description": "User updated successfully", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "This endpoint creates a new user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Create user", "parameters": [{"description": "Create User Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.CreateUserReq"}}], "responses": {"201": {"description": "User created", "schema": {"type": "string"}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "This endpoint deletes a user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Delete user", "responses": {"200": {"description": "User deleted successfully", "schema": {"type": "string"}}, "404": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}, "definitions": {"dtos.AddContact": {"type": "object", "properties": {"first_name": {"type": "string"}, "group_id": {"type": "string"}, "last_name": {"type": "string"}, "number": {"type": "string"}}}, "dtos.BlackList": {"type": "object", "properties": {"device_number": {"type": "string"}, "number": {"type": "string"}}}, "dtos.ChatMessagesReq": {"type": "object", "properties": {"chat_id": {"type": "string"}, "reg_id": {"type": "string"}}}, "dtos.ChatMessagesRes": {"type": "object", "properties": {"from": {"type": "string"}, "message": {"type": "string"}, "send_time": {"type": "string"}}}, "dtos.ChatRes": {"type": "object", "properties": {"chat_id": {"type": "string"}, "message": {"type": "string"}, "send_time": {"type": "string"}}}, "dtos.CheckActiveDeviceData": {"type": "object", "properties": {"device_number": {"type": "string"}, "j_id": {"type": "string"}, "platform": {"type": "string"}, "user_name": {"type": "string"}}}, "dtos.CheckActiveDeviceRes": {"type": "object", "properties": {"data": {"$ref": "#/definitions/dtos.CheckActiveDeviceData"}, "is_connected": {"type": "boolean"}, "message": {"type": "string"}}}, "dtos.CheckDeviceRes": {"type": "object", "properties": {"is_connected": {"type": "boolean"}, "message": {"type": "string"}}}, "dtos.CodeResponse": {"type": "object", "properties": {"code": {"type": "string"}, "regId": {"type": "string"}}}, "dtos.ConnectWpFailRes": {"type": "object", "properties": {"device_limit_exceed": {"type": "boolean"}, "error": {"type": "string"}}}, "dtos.ConnectWpReq": {"type": "object", "properties": {"phone": {"type": "string"}}}, "dtos.Contact": {"type": "object", "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "number": {"type": "string"}}}, "dtos.CreateUserReq": {"type": "object", "properties": {"country_code": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "password": {"type": "string"}, "phone": {"type": "string"}}}, "dtos.Device": {"type": "object", "properties": {"business_name": {"type": "string"}, "device_number": {"type": "string"}, "j_id": {"type": "string"}, "platform": {"type": "string"}, "push_name": {"type": "string"}, "registration_id": {"type": "string"}, "state": {"description": "1- online 2- <PERSON>ık<PERSON>ş", "type": "string"}}}, "dtos.Group": {"type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}}}, "dtos.LoginRequest": {"type": "object", "required": ["identify", "password"], "properties": {"identify": {"type": "string"}, "password": {"type": "string"}}}, "dtos.LoginResponse": {"type": "object", "properties": {"Expires": {"type": "string"}, "IsSucceeded": {"type": "boolean"}, "Token": {"type": "string"}}}, "dtos.MessageCallBack": {"type": "object", "properties": {"callback_data": {"type": "string"}, "content": {"type": "string"}, "phone": {"type": "string"}, "send_time": {"type": "string"}, "status": {"description": "1: Sended 2: Not Sended", "type": "string"}}}, "dtos.MessageCallBackArray": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/definitions/dtos.MessageCallBack"}}}}, "dtos.QrResponse": {"type": "object", "properties": {"qr": {"type": "string"}, "regId": {"type": "string"}}}, "dtos.Report": {"type": "object", "properties": {"failed": {"type": "integer"}, "successed": {"type": "integer"}}}, "dtos.SendMessageDto": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}, "poll_option": {"type": "array", "items": {"type": "string"}}, "receivers": {"type": "array", "items": {"type": "string"}}, "reg_id": {"type": "string"}, "reply_option": {"type": "array", "items": {"type": "string"}}, "selectable_option_count": {"type": "integer"}, "send_time": {"type": "string"}, "user_id": {"type": "string"}}}, "dtos.UpdateContact": {"type": "object", "properties": {"contact_id": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "number": {"type": "string"}}}, "dtos.UserRes": {"type": "object", "properties": {"country_code": {"type": "string"}, "first_name": {"type": "string"}, "id": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}}}, "dtos.UserUpdate": {"type": "object", "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "password": {"type": "string"}}}, "dtos.WhatsappLogoutReq": {"type": "object", "properties": {"reg_id": {"type": "string"}}}, "entities.Message": {"type": "object", "properties": {"content": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "created_at": {"type": "string", "example": "2021-01-01T00:00:00Z"}, "deleted_at": {"type": "string", "format": "date-time"}, "id": {"type": "string", "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"}, "is_sent": {"type": "boolean", "default": false, "example": true}, "receiver": {"description": "phone numbers must be separated by commas", "type": "string", "example": "90555555555,905555555556"}, "reg_id": {"type": "string", "example": "1234567"}, "report_id": {"type": "string", "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"}, "send_time": {"type": "string", "example": "2006-01-02 15:04:05"}, "status": {"description": "1-processing, 2-paused, 3-successed, 4-failed, 5-future message 6-canceled", "type": "string"}, "updated_at": {"type": "string", "example": "2021-01-01T00:00:00Z"}, "user_id": {"type": "string", "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}