package routes

import (
	"net/http"
	blacklist "whatsapp/pkg/domains/black_list"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
)

// TODO: bu kısım eksik tammalanması lazım!!!
func BlackListRoutes(r *gin.RouterGroup, s blacklist.Service) {
	blackList := r.Group("/blacklist")
	blackList.POST("", middlewares.Authorized(), AddBlackList(s))
	blackList.GET("", middlewares.Authorized(), GetBlackList(s))
	blackList.GET("/stats", middlewares.Authorized(), GetBlackListStats(s))
	blackList.DELETE("/:id", middlewares.Authorized(), RemoveBlackList(s))
}

// AddBlackList handles the addition of a number to the blacklist.
// @Summary Add number to blacklist
// @Description This endpoint adds a number to the blacklist.
// @Tags Blacklist
// @Accept  json
// @Produce  json
// @Param body body dtos.BlackList true "Blacklist Request"
// @Success 200 {object} map[string]interface{} "success message"
// @Failure 400 {object} map[string]string "error message"
// @Router /blacklist [post]
func AddBlackList(s blacklist.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.BlackList
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}
		userId := state.GetCurrentUserID(c)
		err = s.AddBlackList(c, userId, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return

		}
		c.JSON(http.StatusCreated, "blacklist added")
	}
}

// GetBlackList handles getting all blacklisted numbers for a user.
// @Summary Get blacklist
// @Description This endpoint gets all blacklisted numbers for the authenticated user.
// @Tags Blacklist
// @Accept  json
// @Produce  json
// @Success 200 {array} entities.BlackList "blacklist items"
// @Failure 400 {object} map[string]string "error message"
// @Router /blacklist [get]
func GetBlackList(s blacklist.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		blacklistItems, err := s.GetBlackList(c, userId)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, blacklistItems)
	}
}

// RemoveBlackList handles removing a number from the blacklist.
// @Summary Remove number from blacklist
// @Description This endpoint removes a number from the blacklist.
// @Tags Blacklist
// @Accept  json
// @Produce  json
// @Param id path string true "Blacklist ID"
// @Success 200 {object} map[string]interface{} "success message"
// @Failure 400 {object} map[string]string "error message"
// @Router /blacklist/{id} [delete]
func RemoveBlackList(s blacklist.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		userId := state.GetCurrentUserID(c)
		err := s.RemoveBlackList(c, userId, id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, "blacklist item removed")
	}
}

// GetBlackListStats handles getting blacklist statistics for a user.
// @Summary Get blacklist statistics
// @Description This endpoint gets blacklist statistics for the authenticated user.
// @Tags Blacklist
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.BlackListStats "blacklist statistics"
// @Failure 400 {object} map[string]string "error message"
// @Router /blacklist/stats [get]
func GetBlackListStats(s blacklist.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		stats, err := s.GetBlackListStats(c, userId)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, stats)
	}
}
