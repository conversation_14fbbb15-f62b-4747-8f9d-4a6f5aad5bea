package routes

import (
	"fmt"
	"net/http"
	"whatsapp/pkg/domains/device"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
)

func DeviceRoutes(r *gin.RouterGroup, s device.Service) {
	device := r.Group("/devices")
	device.GET("/qr", middlewares.Authorized(), getQr(s))
	device.POST("/code", middlewares.Authorized(), getCode(s))

	device.POST("/check", middlewares.Authorized(), checkDevice(s))
	device.POST("/check/active", middlewares.Authorized(), checkActiveDevice(s))

	device.GET("", middlewares.Authorized(), getDevices(s))

	device.POST("/logout", middlewares.Authorized(), logout(s))
	device.POST("/logout2", logout2(s))
}

// getQr handles the retrieval of a QR code.
// @Summary Get QR code
// @Description This endpoint retrieves a QR code for the user.
// @Tags Device
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.QrResponse "QR code response"
// @Failure 404 {object} dtos.ConnectWpFailRes "error message"
// @Router /devices/qr [get]
func getQr(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		qrRes, err, isExceed := s.GetQr(userId, c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, dtos.ConnectWpFailRes{Error: err.Error(), DeviceLimitExceed: isExceed})
			return
		}
		c.JSON(200, qrRes)
	}
}

// getCode handles the retrieval of a code.
// @Summary Get code
// @Description This endpoint retrieves a code for the user.
// @Tags Device
// @Accept  json
// @Produce  json
// @Param body body dtos.ConnectWpReq true "Connect WP Request"
// @Success 200 {object} dtos.CodeResponse "code response"
// @Failure 400 {object} map[string]string "error message"
// @Router /devices/code [post]
func getCode(s device.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.ConnectWpReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}
		userId := state.GetCurrentUserID(c)
		codeRes, err, isExceed := s.GetCode(userId, req, c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, dtos.ConnectWpFailRes{Error: err.Error(), DeviceLimitExceed: isExceed})
			return
		}
		c.JSON(200, codeRes)
	}
}

// checkDevice handles the checking of a device.
// @Summary Check device
// @Description This endpoint checks if a device is registered.
// @Tags Device
// @Accept  json
// @Produce  json
// @Param reg_id query string true "Registration ID"
// @Success 200 {object} dtos.CheckDeviceRes "device check response"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "error message"
// @Router /devices/check [get]
func checkDevice(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		regId := c.Query("reg_id")
		if regId == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, "Invalid reg_id")
			return
		}
		userId := state.GetCurrentUserID(c)
		checkDevice, err := s.CheckDevice(regId, userId, c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(200, checkDevice)
	}
}

// checkActiveDevice handles the checking of an active device.
// @Summary Check active device
// @Description This endpoint checks if a device is active.
// @Tags Device
// @Accept  json
// @Produce  json
// @Param reg_id query string true "Registration ID"
// @Success 200 {object} dtos.CheckActiveDeviceRes "active device check response"
// @Failure 404 {object} map[string]string "error message"
// @Router /devices/check-active [get]
func checkActiveDevice(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		regId := c.Query("reg_id")
		userId := state.GetCurrentUserID(c)
		checkActiveDevice, err := s.CheckActiveDevice(userId, regId, c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(200, checkActiveDevice)
	}
}

// getDevices handles the retrieval of devices.
// @Summary Get devices
// @Description This endpoint retrieves a list of devices for the user.
// @Tags Device
// @Accept  json
// @Produce  json
// @Param page query int false "Page number"
// @Param per_page query int false "Items per page"
// @Success 200 {array} dtos.Device "list of devices"
// @Failure 500 {object} map[string]string "error message"
// @Router /devices/list [get]
func getDevices(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		devices, err := s.GetDevice(userId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, devices)
	}
}

// logout handles the logout from a device.
// @Summary Logout from device
// @Description This endpoint logs out the user from a device.
// @Tags Device
// @Accept  json
// @Produce  json
// @Param body body dtos.WhatsappLogoutReq true "Logout Request"
// @Success 200 {string} string "success message"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /devices/logout [post]
func logout(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.WhatsappLogoutReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}
		err := s.Logout(req.RegId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, fmt.Sprintf("Successfully logged out from device-id: %v", req.RegId))
	}
}

func logout2(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.WhatsappLogoutReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}
		err := s.Logout2(req.RegId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, fmt.Sprintf("Successfully logged out from device-id: %v", req.RegId))
	}
}
