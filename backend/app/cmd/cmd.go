package cmd

import (
	"whatsapp/pkg/config"
	"whatsapp/pkg/infrastructure/database"
	"whatsapp/pkg/infrastructure/server"
)

func Execute() {
	database.Connect(config.ReadValue().Database)

	server.LaunchHttpServer(
		config.ReadValue().Host,
		config.ReadValue().Port,
		config.ReadValue().AppName,
		config.ReadValue().AllowMethods,
		config.ReadValue().AllowOrigins,
		config.ReadValue().AllowHeaders,
	)

}
