app_name: Whatsapp
host: 
port: 3000
grpc_port: 50051

database:
    host: whatsappdb
    port: 5432
    name: whatsapp
    user: whatsapp
    password: whatsapp
    sslmode: disable
    migrate: true

jwt_secret: "secret"
jwt_store_secret: "store_secret"
jwt_expire: 9000

wp_api_url:
api_key:
cancel_link:

redis:
    host: "whatsapp_redis"
    port: "6379"
    password: ""
    db: 0 

allow_methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS

allow_headers:
  - Content-Type
  - Authorization
  - X-HASH
  - sentry-trace
  - x-mono-auth

allow_origins:
  - http://localhost:7070