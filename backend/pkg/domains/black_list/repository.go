package blacklist

import (
	"context"
	"time"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	AddBlackList(ctx context.Context, userId uuid.UUID, countryCode, number string) error
	GetBlackList(ctx context.Context, userId uuid.UUID) ([]entities.BlackList, error)
	RemoveBlackList(ctx context.Context, userId uuid.UUID, id string) error
	GetBlackListStats(ctx context.Context, userId uuid.UUID) (dtos.BlackListStats, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) AddBlackList(ctx context.Context, userId uuid.UUID, countryCode, number string) error {
	var blackList entities.BlackList
	blackList.CountryCode = countryCode
	blackList.Number = number
	blackList.UserID = userId
	return r.db.Model(&entities.BlackList{}).Create(&blackList).Error
}

func (r *repository) GetBlackList(ctx context.Context, userId uuid.UUID) ([]entities.BlackList, error) {
	var blackList []entities.BlackList
	err := r.db.Where("user_id = ?", userId).Find(&blackList).Error
	return blackList, err
}

func (r *repository) RemoveBlackList(ctx context.Context, userId uuid.UUID, id string) error {
	return r.db.Where("id = ? AND user_id = ?", id, userId).Delete(&entities.BlackList{}).Error
}

func (r *repository) GetBlackListStats(ctx context.Context, userId uuid.UUID) (dtos.BlackListStats, error) {
	var stats dtos.BlackListStats

	// Total blacklisted count
	var totalCount int64
	err := r.db.Model(&entities.BlackList{}).Where("user_id = ?", userId).Count(&totalCount).Error
	if err != nil {
		return stats, err
	}
	stats.TotalBlacklisted = int(totalCount)

	// Added this month
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var addedThisMonth int64
	err = r.db.Model(&entities.BlackList{}).
		Where("user_id = ? AND created_at >= ?", userId, startOfMonth).
		Count(&addedThisMonth).Error
	if err != nil {
		return stats, err
	}
	stats.AddedThisMonth = int(addedThisMonth)

	// Removed this month (soft deleted records)
	var removedThisMonth int64
	err = r.db.Unscoped().Model(&entities.BlackList{}).
		Where("user_id = ? AND deleted_at >= ? AND deleted_at IS NOT NULL", userId, startOfMonth).
		Count(&removedThisMonth).Error
	if err != nil {
		return stats, err
	}
	stats.RemovedThisMonth = int(removedThisMonth)

	return stats, nil
}
