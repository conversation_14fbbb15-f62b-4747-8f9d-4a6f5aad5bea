package message

import (
	"context"
	"errors"
	"strings"
	"whatsapp/pkg/domains/device"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"
	"whatsapp/pkg/infrastructure/database"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nyaruka/phonenumbers"
)

type service struct {
	repository Repository
	deviceRepo device.Repository
}

type Service interface {
	MessageCallBack(req dtos.MessageCallBackArray, ctx context.Context) error
	SendMessage(c *gin.Context, messageDto dtos.SendMessageDto, ctx context.Context) error

	GetChats(regId string, ctx context.Context) ([]dtos.ChatRes, error)
	GetChatMessages(req dtos.ChatMessagesReq, userId uuid.UUID, ctx context.Context) ([]dtos.ChatMessagesRes, error)

	GetReports(userId uuid.UUID, ctx context.Context) (dtos.Report, error)
	GetMessages(userId uuid.UUID, status, page, perPage, initialTime, endTime string, ctx context.Context) (dtos.MessagesResponse, error)
	GetDashboardStats(userId uuid.UUID, status, initialTime, endTime string, ctx context.Context) (dtos.DashboardStats, error)

	// Future Message methods
	UpdateFutureMessage(c *gin.Context, req dtos.FutureMessageUpdate) error
	DeleteFutureMessage(req dtos.FutureMessageDelete) error

	// Queue Message methods
	PauseMessage(req dtos.PauseMessage) error
	DeleteMessage(req dtos.DeleteMessage) error
}

func NewService(r Repository, dR device.Repository) Service {
	return &service{
		repository: r,
		deviceRepo: dR,
	}
}

func (s *service) MessageCallBack(req dtos.MessageCallBackArray, ctx context.Context) error {
	return s.repository.MessageCallBack(req, ctx)
}

func (s *service) SendMessage(c *gin.Context, messageDto dtos.SendMessageDto, ctx context.Context) error {
	numbers, err := removeDuplicatesAndBlockedNumbers(messageDto.UserId, messageDto.Receivers)
	if err != nil {
		return err
	}

	if len(numbers) == 0 {
		return errors.New("no valid numbers to send message")
	}

	var newNumbers []string
	for _, num := range numbers {
		num = strings.ReplaceAll(num, " ", "")
		isValid, err := validatePhoneNumber(num)
		if !isValid || err != nil {
			continue
		}
		newNumbers = append(newNumbers, num)
	}
	messageDto.Receivers = newNumbers
	return s.repository.SaveMessage(c, messageDto, ctx)
}

func (s *service) GetChats(regId string, ctx context.Context) ([]dtos.ChatRes, error) {
	return s.repository.GetChats(regId, ctx)
}

func (s *service) GetChatMessages(req dtos.ChatMessagesReq, userId uuid.UUID, ctx context.Context) ([]dtos.ChatMessagesRes, error) {
	return s.repository.GetChatMessages(req, ctx)
}

func (s *service) GetReports(userId uuid.UUID, ctx context.Context) (dtos.Report, error) {
	return s.repository.GetReports(userId, ctx)
}

func (s *service) GetMessages(userId uuid.UUID, status, page, perPage, initialTime, endTime string, ctx context.Context) (dtos.MessagesResponse, error) {
	return s.repository.GetMessages(userId, status, page, perPage, initialTime, endTime, ctx)
}

func (s *service) GetDashboardStats(userId uuid.UUID, status, initialTime, endTime string, ctx context.Context) (dtos.DashboardStats, error) {
	return s.repository.GetDashboardStats(userId, status, initialTime, endTime, ctx)
}

// Future Message methods
func (s *service) UpdateFutureMessage(c *gin.Context, req dtos.FutureMessageUpdate) error {
	ctx := c.Request.Context()
	return s.repository.UpdateFutureMessage(c, req, ctx)
}

func (s *service) DeleteFutureMessage(req dtos.FutureMessageDelete) error {
	ctx := context.Background()
	return s.repository.DeleteFutureMessage(req, ctx)
}

// Queue Message methods
func (s *service) PauseMessage(req dtos.PauseMessage) error {
	ctx := context.Background()
	return s.repository.PauseMessage(req, ctx)
}

func (s *service) DeleteMessage(req dtos.DeleteMessage) error {
	ctx := context.Background()
	return s.repository.DeleteMessage(req, ctx)
}

func removeDuplicatesAndBlockedNumbers(userId uuid.UUID, numbers []string) ([]string, error) {
	db := database.ClientDB()

	// Blacklist'teki tüm numaraları al
	var blacklistEntries []entities.BlackList
	err := db.Model(&entities.BlackList{}).
		Where("user_id = ?", userId).
		Find(&blacklistEntries).Error
	if err != nil {
		return nil, err
	}

	// Blacklist set'i oluştur - hem country_code+number hem de sadece number kontrolü için
	blacklistSet := make(map[string]bool)
	for _, entry := range blacklistEntries {
		// Country code varsa birleştir
		if entry.CountryCode != "" {
			fullNumber := "+" + entry.CountryCode + entry.Number
			blacklistSet[fullNumber] = true
		}
		// Sadece number'ı da ekle
		blacklistSet[entry.Number] = true
	}

	freqMap := make(map[string]bool)
	uniqueNumbers := []string{}
	for _, num := range numbers {
		isBlacklisted := false

		// Direkt numara kontrolü
		if blacklistSet[num] {
			isBlacklisted = true
		}

		// Eğer numara + ile başlıyorsa, country code'u ayırıp sadece number kısmını da kontrol et
		if strings.HasPrefix(num, "+") && len(num) > 3 {
			// İlk 3 karakter country code olarak kabul et
			countryCode := num[1:3]
			phoneNumber := num[3:]

			// Country code + number kombinasyonunu kontrol et
			if blacklistSet["+"+countryCode+phoneNumber] || blacklistSet[phoneNumber] {
				isBlacklisted = true
			}
		}

		if !freqMap[num] && !isBlacklisted {
			uniqueNumbers = append(uniqueNumbers, num)
			freqMap[num] = true
		}
	}

	return uniqueNumbers, nil
}

func validatePhoneNumber(phoneNumber string) (bool, error) {
	num, err := phonenumbers.Parse(phoneNumber, "")
	if err != nil {
		return false, err
	}
	return phonenumbers.IsValidNumber(num), nil
}
