package message

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"whatsapp/pkg/config"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	SaveMessage(c *gin.Context, messageDto dtos.SendMessageDto, ctx context.Context) error
	MessageCallBack(req dtos.MessageCallBackArray, ctx context.Context) error

	GetChats(regId string, ctx context.Context) ([]dtos.ChatRes, error)
	GetChatMessages(chatMessagesReq dtos.ChatMessagesReq, ctx context.Context) ([]dtos.ChatMessagesRes, error)

	GetReports(userId uuid.UUID, ctx context.Context) (dtos.Report, error)
	GetMessages(userId uuid.UUID, status, page, perPage, initialTime, endTime string, ctx context.Context) (dtos.MessagesResponse, error)
	GetDashboardStats(userId uuid.UUID, status, initialTime, endTime string, ctx context.Context) (dtos.DashboardStats, error)

	// Future Message methods
	UpdateFutureMessage(c *gin.Context, req dtos.FutureMessageUpdate, ctx context.Context) error
	DeleteFutureMessage(req dtos.FutureMessageDelete, ctx context.Context) error

	// Queue Message methods
	PauseMessage(req dtos.PauseMessage, ctx context.Context) error
	DeleteMessage(req dtos.DeleteMessage, ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) SaveMessage(c *gin.Context, messageDto dtos.SendMessageDto, ctx context.Context) error {
	var user entities.User
	// var checkActiveDevice dtos.CheckActiveDeviceRes

	tx := r.db.Begin()

	err := wpCreditUpdate(tx, messageDto.UserId, len(messageDto.Receivers), ctx)
	if err != nil {
		tx.Rollback()
		return err
	}

	reportId := uuid.New()
	messages, err := messageDto.WhatsappMessageToEntity(reportId)
	if err != nil {
		tx.Rollback()
		return err
	}

	for _, message := range messages {
		err := tx.WithContext(ctx).Create(&message).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	err = tx.Where("id = ?", messageDto.UserId).First(&user).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	payload := map[string]string{
		"message":   messageDto.Content,
		"reg_id":    messageDto.RegID,
		"report_id": reportId.String(),
		"send_time": messageDto.SendTime,
		"format":    messageDto.Format,
	}

	url := config.ReadValue().WpApiUrl + "/message/oneToN"

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	form, _ := c.MultipartForm()
	if form != nil {
		files := form.File["file"]
		if len(files) > 0 {
			for _, fl := range files {
				file, err := fl.Open()
				if err != nil {
					tx.Rollback()
					return err
				}
				defer file.Close()

				part, err := writer.CreateFormFile("file", fl.Filename)
				if err != nil {
					tx.Rollback()
					return err
				}
				_, err = io.Copy(part, file)
				if err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	if messageDto.CancelLink && user.CancelLinkEndTime.After(time.Now()) {
		_ = writer.WriteField("cancel_link", "true")
	} else {
		_ = writer.WriteField("cancel_link", "false")
	}

	for key, val := range payload {
		_ = writer.WriteField(key, val)
	}
	for _, val := range messageDto.Receivers {
		_ = writer.WriteField("to", val)
	}
	if len(messageDto.PollOption) > 0 {
		for _, val := range messageDto.PollOption {
			_ = writer.WriteField("poll_option", val)
		}

		_ = writer.WriteField("selectable_option_count", strconv.Itoa(messageDto.SelectableOptionCount))
	}
	if len(messageDto.ReplyOption) > 0 {
		for _, val := range messageDto.ReplyOption {
			_ = writer.WriteField("reply_option", val)
		}
	}
	err = writer.Close()
	if err != nil {
		tx.Rollback()
		return err
	}
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		tx.Rollback()
		return err
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to send message")
	}
	tx.Commit()
	return nil
}

func (r *repository) MessageCallBack(req dtos.MessageCallBackArray, ctx context.Context) error {
	tx := r.db.Begin()

	for _, message := range req.Messages {
		switch message.Status {
		case "1": // Sended
			if err := tx.WithContext(ctx).Model(&entities.Message{}).Where("report_id = ? and receiver = ?", message.CallBackData, message.Phone).Update("is_sent", true).Update("status", 3).Error; err != nil {
				tx.Rollback()
				return err
			}
		case "2": // Not Sended
			if err := tx.WithContext(ctx).Model(&entities.Message{}).Where("report_id = ? and receiver = ?", message.CallBackData, message.Phone).Update("is_sent", false).Update("status", 4).Error; err != nil {
				tx.Rollback()
				return err
			}
		default:
			tx.Rollback()
			return fmt.Errorf("invalid status value")
		}
	}
	tx.Commit()
	return nil
}

func (r *repository) GetChats(regId string, ctx context.Context) ([]dtos.ChatRes, error) {
	var chatRes []dtos.ChatRes
	baseURL, err := url.Parse(config.ReadValue().WpApiUrl)
	if err != nil {
		return nil, err
	}

	baseURL.Path += "/message/chats"
	params := url.Values{}
	params.Add("reg_id", regId)
	baseURL.RawQuery = params.Encode()
	req, err := http.NewRequestWithContext(ctx, "GET", baseURL.String(), nil)
	if err != nil {
		return []dtos.ChatRes{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		return []dtos.ChatRes{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return []dtos.ChatRes{}, err
		}
		err = json.Unmarshal(resBody, &chatRes)
		if err != nil {
			return []dtos.ChatRes{}, err
		}
	} else {
		return []dtos.ChatRes{}, errors.New("failed to get device")
	}
	return chatRes, nil
}

func (r *repository) GetChatMessages(chatMessagesReq dtos.ChatMessagesReq, ctx context.Context) ([]dtos.ChatMessagesRes, error) {
	var chatMessagesRes []dtos.ChatMessagesRes
	baseURL, err := url.Parse(config.ReadValue().WpApiUrl)
	if err != nil {
		return []dtos.ChatMessagesRes{}, err
	}

	baseURL.Path += "/message/chat/messages"
	params := url.Values{}
	params.Add("chat_id", chatMessagesReq.ChatId)
	params.Add("reg_id", chatMessagesReq.RegId)
	baseURL.RawQuery = params.Encode()
	req, err := http.NewRequestWithContext(ctx, "GET", baseURL.String(), nil)
	if err != nil {
		return []dtos.ChatMessagesRes{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		return []dtos.ChatMessagesRes{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return []dtos.ChatMessagesRes{}, err
		}
		err = json.Unmarshal(resBody, &chatMessagesRes)
		if err != nil {
			return []dtos.ChatMessagesRes{}, err
		}
	} else {
		return []dtos.ChatMessagesRes{}, errors.New("failed to get device")
	}
	return chatMessagesRes, nil
}

func (r *repository) GetReports(userId uuid.UUID, ctx context.Context) (dtos.Report, error) {
	var (
		successed, failed int64
	)
	err := r.db.Model(&entities.Message{}).Where("user_id = ? and status = ?", userId, 3).Count(&successed).Error
	if err != nil {
		return dtos.Report{}, err
	}

	err = r.db.Model(&entities.Message{}).Where("user_id = ? and status = ?", userId, 4).Count(&failed).Error
	if err != nil {
		return dtos.Report{}, err
	}

	return dtos.Report{Successed: int(successed), Failed: int(failed)}, nil
}

func (r *repository) GetMessages(userId uuid.UUID, status, page, perPage, initialTime, endTime string, ctx context.Context) (dtos.MessagesResponse, error) {
	var messages []entities.Message
	query := r.db.Model(&entities.Message{}).Where("user_id = ?", userId)

	if status != "" {
		// Handle multiple status values separated by comma
		statusList := strings.Split(status, ",")
		if len(statusList) > 1 {
			// Convert string status values to integers
			var statusInts []int
			for _, s := range statusList {
				if statusInt, err := strconv.Atoi(strings.TrimSpace(s)); err == nil {
					statusInts = append(statusInts, statusInt)
				}
			}
			if len(statusInts) > 0 {
				query = query.Where("status IN ?", statusInts)
			}
		} else {
			// Convert single status to integer
			if statusInt, err := strconv.Atoi(strings.TrimSpace(status)); err == nil {
				query = query.Where("status = ?", statusInt)
			}
		}
	}

	if initialTime != "" && endTime != "" {
		query = query.Where("created_at BETWEEN ? AND ?", initialTime, endTime)
	}

	pageNum, _ := strconv.Atoi(page)
	perPageNum, _ := strconv.Atoi(perPage)

	if pageNum > 0 && perPageNum > 0 {
		offset := (pageNum - 1) * perPageNum
		query = query.Offset(offset).Limit(perPageNum)
	}

	if err := query.Find(&messages).Error; err != nil {
		return dtos.MessagesResponse{}, err
	}

	response := dtos.MessagesResponse{
		Messages: messages,
	}

	return response, nil
}

func (r *repository) GetDashboardStats(userId uuid.UUID, status, initialTime, endTime string, ctx context.Context) (dtos.DashboardStats, error) {
	statsQuery := r.db.Model(&entities.Message{}).Where("user_id = ?", userId)

	if status != "" {
		// Handle multiple status values separated by comma
		statusList := strings.Split(status, ",")
		if len(statusList) > 1 {
			// Convert string status values to integers
			var statusInts []int
			for _, s := range statusList {
				if statusInt, err := strconv.Atoi(strings.TrimSpace(s)); err == nil {
					statusInts = append(statusInts, statusInt)
				}
			}
			if len(statusInts) > 0 {
				statsQuery = statsQuery.Where("status IN ?", statusInts)
			}
		} else {
			// Convert single status to integer
			if statusInt, err := strconv.Atoi(strings.TrimSpace(status)); err == nil {
				statsQuery = statsQuery.Where("status = ?", statusInt)
			}
		}
	}

	if initialTime != "" && endTime != "" {
		statsQuery = statsQuery.Where("created_at BETWEEN ? AND ?", initialTime, endTime)
	}

	// Calculate statistics
	var successMessages, failedMessages, processingMessages, pausedMessages, canceledMessages int64

	statsQuery.Where("status = ?", 3).Count(&successMessages)
	statsQuery.Where("status = ?", 4).Count(&failedMessages)
	statsQuery.Where("status = ?", 1).Count(&processingMessages)
	statsQuery.Where("status = ?", 2).Count(&pausedMessages)
	statsQuery.Where("status = ?", 6).Count(&canceledMessages)

	// Total messages = only success + failed
	totalMessages := successMessages + failedMessages

	response := dtos.DashboardStats{
		TotalMessages:      int(totalMessages),
		SuccessMessages:    int(successMessages),
		FailedMessages:     int(failedMessages),
		ProcessingMessages: int(processingMessages),
		PausedMessages:     int(pausedMessages),
		CanceledMessages:   int(canceledMessages),
	}

	return response, nil
}

func (r *repository) UpdateFutureMessage(c *gin.Context, req dtos.FutureMessageUpdate, ctx context.Context) error {
	tx := r.db.Begin()

	err := tx.WithContext(ctx).Model(&entities.Message{}).Where("id = ? AND status = ?", req.ID, 5).Updates(map[string]interface{}{
		"content":   req.Message,
		"receiver":  req.Numbers,
		"send_time": req.TimePost,
		"reg_id":    req.RegId,
	}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	url := config.ReadValue().WpApiUrl + "/message/futuremessage"

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	_ = writer.WriteField("id", req.ID)
	_ = writer.WriteField("name", req.Name)
	_ = writer.WriteField("numbers", req.Numbers)
	_ = writer.WriteField("message", req.Message)
	_ = writer.WriteField("time_post", req.TimePost)
	_ = writer.WriteField("type", fmt.Sprintf("%d", req.Type))
	_ = writer.WriteField("reg_id", req.RegId)

	form, _ := c.MultipartForm()
	if form != nil {
		files := form.File["file"]
		if len(files) > 0 {
			for _, fl := range files {
				file, err := fl.Open()
				if err != nil {
					tx.Rollback()
					return err
				}
				defer file.Close()

				part, err := writer.CreateFormFile("file", fl.Filename)
				if err != nil {
					tx.Rollback()
					return err
				}
				_, err = io.Copy(part, file)
				if err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	err = writer.Close()
	if err != nil {
		tx.Rollback()
		return err
	}

	httpReq, err := http.NewRequest("PUT", url, body)
	if err != nil {
		tx.Rollback()
		return err
	}
	httpReq.Header.Set("Content-Type", writer.FormDataContentType())
	httpReq.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to update future message")
	}

	tx.Commit()
	return nil
}

func (r *repository) DeleteFutureMessage(req dtos.FutureMessageDelete, ctx context.Context) error {
	tx := r.db.Begin()
	var message entities.Message
	if err := tx.WithContext(ctx).First(&message, "id = ?", req.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	err := tx.WithContext(ctx).Where("id = ?", req.ID).Delete(&entities.Message{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	url := fmt.Sprintf("%s/message/futuremessage?id=%s", config.ReadValue().WpApiUrl, message.ReportId)

	httpReq, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		tx.Rollback()
		return err
	}
	httpReq.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to delete future message")
	}

	tx.Commit()
	return nil
}

func (r *repository) PauseMessage(req dtos.PauseMessage, ctx context.Context) error {
	tx := r.db.Begin()

	var newStatus int
	if req.IsPause {
		newStatus = 2 // paused
	} else {
		newStatus = 1 // processing
	}

	err := tx.WithContext(ctx).Model(&entities.Message{}).Where("id = ? AND status IN ?", req.ID, []int{1, 2}).Update("status", newStatus).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	url := config.ReadValue().WpApiUrl + "/message/queue"

	payload := map[string]interface{}{
		"id":       req.ID,
		"is_pause": req.IsPause,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		tx.Rollback()
		return err
	}

	httpReq, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		tx.Rollback()
		return err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to pause/resume message")
	}

	tx.Commit()
	return nil
}

func (r *repository) DeleteMessage(req dtos.DeleteMessage, ctx context.Context) error {
	tx := r.db.Begin()
	var message entities.Message
	err := tx.WithContext(ctx).Model(&entities.Message{}).Where("id = ? ", req.ID).First(&message).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.WithContext(ctx).Where("id = ? ", req.ID).Delete(&entities.Message{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	url := fmt.Sprintf("%s/message/queue?id=%s", config.ReadValue().WpApiUrl, message.ReportId)

	httpReq, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		tx.Rollback()
		return err
	}
	httpReq.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to delete message")
	}

	tx.Commit()
	return nil
}

func wpCreditUpdate(db *gorm.DB, userId uuid.UUID, credit int, ctx context.Context) error {
	var totalCredit int
	err := db.WithContext(ctx).Model(&entities.User{}).Select("wp_credit").Where("id = ?", userId).Scan(&totalCredit).Error
	if err != nil {
		return err
	}
	if credit > totalCredit {
		return fmt.Errorf("insufficient credit. you have %v credit", totalCredit)
	}
	totalCredit -= credit
	err = db.WithContext(ctx).Model(&entities.User{}).Where("id = ?", userId).Update("wp_credit", totalCredit).Error
	if err != nil {
		return err
	}
	return nil
}
