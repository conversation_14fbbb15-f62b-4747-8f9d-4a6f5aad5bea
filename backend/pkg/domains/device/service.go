package device

import (
	"context"
	"whatsapp/pkg/dtos"

	"github.com/google/uuid"
)

type service struct {
	repository Repository
	// userRepository user.Repository
}

type Service interface {
	GetQr(userId uuid.UUID, ctx context.Context) (dtos.QrResponse, error, bool)
	GetCode(userId uuid.UUID, req dtos.ConnectWpReq, ctx context.Context) (dtos.CodeResponse, error, bool)
	CheckPhone(userId uuid.UUID, phone string) (bool, error)
	CheckDevice(regId string, userId uuid.UUID, ctx context.Context) (dtos.CheckDeviceRes, error)
	CheckActiveDevice(userId uuid.UUID, regId string, ctx context.Context) (dtos.CheckActiveDeviceRes, error)
	GetDevice(userId uuid.UUID, ctx context.Context) ([]dtos.Device, error)
	Logout(regId string, ctx context.Context) error
	Logout2(regId string, ctx context.Context) error
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetQr(userId uuid.UUID, ctx context.Context) (dtos.QrResponse, error, bool) {
	err, isExceed := s.repository.CheckDeviceCount(userId, ctx)
	if err != nil {
		return dtos.QrResponse{}, err, isExceed
	}
	qr, err := s.repository.GetQr(ctx)
	if err != nil {
		return dtos.QrResponse{}, err, false
	}
	return qr, nil, false
}

func (s *service) GetCode(userId uuid.UUID, req dtos.ConnectWpReq, ctx context.Context) (dtos.CodeResponse, error, bool) {
	err, isExceed := s.repository.CheckDeviceCount(userId, ctx)
	if err != nil {
		return dtos.CodeResponse{}, err, isExceed
	}
	qr, err := s.repository.GetCode(ctx, req)
	if err != nil {
		return dtos.CodeResponse{}, err, false
	}
	return qr, nil, false
}

func (s *service) CheckPhone(userId uuid.UUID, phone string) (bool, error) {
	isPhoneExist, err := s.repository.CheckPhone(userId, phone)
	if err != nil {
		return isPhoneExist, err
	}
	return isPhoneExist, nil
}

func (s *service) CheckDevice(regId string, userId uuid.UUID, ctx context.Context) (dtos.CheckDeviceRes, error) {
	checkDevice, err := s.repository.CheckDevice(regId, userId, ctx)
	if err != nil {
		return dtos.CheckDeviceRes{}, err
	}
	if !checkDevice.IsConnected {
		return dtos.CheckDeviceRes{}, err
	}
	err = s.repository.SaveRegId(regId, userId, ctx)
	if err != nil {
		return dtos.CheckDeviceRes{}, err
	}
	return checkDevice, nil
}

func (s *service) CheckActiveDevice(userId uuid.UUID, regId string, ctx context.Context) (dtos.CheckActiveDeviceRes, error) {
	return s.repository.CheckActiveDevice(regId, ctx)
}

func (s *service) GetDevice(userId uuid.UUID, ctx context.Context) ([]dtos.Device, error) {
	return s.repository.GetDevice(userId, ctx)
}

func (s *service) Logout(regId string, ctx context.Context) error {
	return s.repository.Logout(regId, ctx)
}

func (s *service) Logout2(regId string, ctx context.Context) error {
	return s.repository.Logout2(regId, ctx)
}
