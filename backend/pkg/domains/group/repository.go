package group

import (
	"context"
	"time"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	ListGroup(userId uuid.UUID, ctx context.Context) ([]dtos.Group, error)
	ListGroupWithStats(userId uuid.UUID, ctx context.Context) (dtos.GroupListResponse, error)
	CreateGroup(userId uuid.UUID, req dtos.Group, ctx context.Context) error
	DeleteGroup(id string, ctx context.Context) error
	UpdateGroup(groupId uuid.UUID, req dtos.Group, ctx context.Context) error
	ListContacts(groupId uuid.UUID, ctx context.Context) ([]dtos.Contact, error)
	AddContact(userId uuid.UUID, req dtos.AddContact, ctx context.Context) error
	DeleteContact(contactId uuid.UUID, ctx context.Context) error
	UpdateContact(req dtos.UpdateContact, ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) ListGroup(userId uuid.UUID, ctx context.Context) ([]dtos.Group, error) {
	var groups []entities.Group

	err := r.db.Where("user_id = ?", userId).Find(&groups).Error
	if err != nil {
		return nil, err
	}

	var groupDTOs []dtos.Group
	for _, g := range groups {
		groupDTOs = append(groupDTOs, dtos.Group{
			ID:          g.ID,
			Name:        g.Name,
			Description: g.Description,
		})
	}

	return groupDTOs, nil
}

func (r *repository) ListGroupWithStats(userId uuid.UUID, ctx context.Context) (dtos.GroupListResponse, error) {
	var groups []entities.Group
	var response dtos.GroupListResponse

	// Get groups with contact count
	err := r.db.Where("user_id = ?", userId).Find(&groups).Error
	if err != nil {
		return response, err
	}

	var groupsWithStats []dtos.GroupWithStats
	totalContacts := 0

	for _, g := range groups {
		// Count contacts for each group
		var contactCount int64
		r.db.Model(&entities.Contact{}).Where("group_id = ?", g.ID).Count(&contactCount)

		groupsWithStats = append(groupsWithStats, dtos.GroupWithStats{
			ID:           g.ID,
			Name:         g.Name,
			Description:  g.Description,
			ContactCount: int(contactCount),
			CreatedAt:    g.CreatedAt,
		})

		totalContacts += int(contactCount)
	}

	// Count groups created this month
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var createdThisMonth int64
	r.db.Model(&entities.Group{}).Where("user_id = ? AND created_at >= ?", userId, startOfMonth).Count(&createdThisMonth)

	response.Groups = groupsWithStats
	response.TotalContacts = totalContacts
	response.CreatedThisMonth = int(createdThisMonth)

	return response, nil
}

func (r *repository) CreateGroup(userId uuid.UUID, req dtos.Group, ctx context.Context) error {
	group := entities.Group{
		UserId:      userId,
		Name:        req.Name,
		Description: req.Description,
	}

	return r.db.Create(&group).Error
}

func (r *repository) DeleteGroup(id string, ctx context.Context) error {
	return r.db.Where("id = ?", id).Delete(&entities.Group{}).Error
}

func (r *repository) UpdateGroup(groupId uuid.UUID, req dtos.Group, ctx context.Context) error {
	return r.db.Model(&entities.Group{}).
		Where("id = ?", groupId).
		Updates(map[string]interface{}{
			"name":        req.Name,
			"description": req.Description,
		}).Error
}

func (r *repository) ListContacts(groupId uuid.UUID, ctx context.Context) ([]dtos.Contact, error) {
	var contacts []entities.Contact

	err := r.db.WithContext(ctx).Where("group_id = ?", groupId).Find(&contacts).Error
	if err != nil {
		return nil, err
	}

	var contactDTOs []dtos.Contact
	for _, c := range contacts {
		contactDTOs = append(contactDTOs, dtos.Contact{
			ID:          c.ID,
			FirstName:   c.FirstName,
			LastName:    c.LastName,
			CountryCode: c.CountryCode,
			Number:      c.Number,
		})
	}

	return contactDTOs, nil
}

func (r *repository) AddContact(userId uuid.UUID, req dtos.AddContact, ctx context.Context) error {
	contact := entities.Contact{
		UserId:      userId,
		GroupId:     req.GroupId,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		CountryCode: req.CountryCode,
		Number:      req.Number,
	}

	return r.db.WithContext(ctx).Create(&contact).Error
}

func (r *repository) DeleteContact(contactId uuid.UUID, ctx context.Context) error {
	return r.db.WithContext(ctx).Where("id = ?", contactId).Delete(&entities.Contact{}).Error
}

func (r *repository) UpdateContact(req dtos.UpdateContact, ctx context.Context) error {
	return r.db.WithContext(ctx).
		Model(&entities.Contact{}).
		Where("id = ?", req.ContactId).
		Updates(map[string]interface{}{
			"first_name":   req.FirstName,
			"last_name":    req.LastName,
			"country_code": req.CountryCode,
			"number":       req.Number,
		}).Error
}
