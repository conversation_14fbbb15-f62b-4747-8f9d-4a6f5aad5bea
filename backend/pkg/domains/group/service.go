package group

import (
	"context"
	"whatsapp/pkg/dtos"

	"github.com/google/uuid"
)

type service struct {
	repository Repository
}

type Service interface {
	ListGroup(userId uuid.UUID, ctx context.Context) ([]dtos.Group, error)
	ListGroupWithStats(userId uuid.UUID, ctx context.Context) (dtos.GroupListResponse, error)
	CreateGroup(userId uuid.UUID, req dtos.Group, ctx context.Context) error
	DeleteGroup(id string, ctx context.Context) error
	UpdateGroup(groupId uuid.UUID, req dtos.Group, ctx context.Context) error

	ListContacts(groupId uuid.UUID, ctx context.Context) ([]dtos.Contact, error)
	AddContact(userId uuid.UUID, req dtos.AddContact, ctx context.Context) error
	DeleteContact(contactId uuid.UUID, ctx context.Context) error
	UpdateContact(req dtos.UpdateContact, ctx context.Context) error
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) ListGroup(userId uuid.UUID, ctx context.Context) ([]dtos.Group, error) {
	return s.repository.ListGroup(userId, ctx)
}

func (s *service) ListGroupWithStats(userId uuid.UUID, ctx context.Context) (dtos.GroupListResponse, error) {
	return s.repository.ListGroupWithStats(userId, ctx)
}

func (s *service) CreateGroup(userId uuid.UUID, req dtos.Group, ctx context.Context) error {
	return s.repository.CreateGroup(userId, req, ctx)
}

func (s *service) DeleteGroup(id string, ctx context.Context) error {
	return s.repository.DeleteGroup(id, ctx)
}

func (s *service) UpdateGroup(groupId uuid.UUID, req dtos.Group, ctx context.Context) error {
	return s.repository.UpdateGroup(groupId, req, ctx)
}

func (s *service) ListContacts(groupId uuid.UUID, ctx context.Context) ([]dtos.Contact, error) {
	return s.repository.ListContacts(groupId, ctx)
}

func (s *service) AddContact(userId uuid.UUID, req dtos.AddContact, ctx context.Context) error {
	return s.repository.AddContact(userId, req, ctx)
}

func (s *service) DeleteContact(contactId uuid.UUID, ctx context.Context) error {
	return s.repository.DeleteContact(contactId, ctx)
}

func (s *service) UpdateContact(req dtos.UpdateContact, ctx context.Context) error {
	return s.repository.UpdateContact(req, ctx)
}
