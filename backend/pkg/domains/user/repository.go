package user

import (
	"context"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"
	"whatsapp/pkg/utils"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetUserById(ctx context.Context, userId uuid.UUID) (dtos.UserRes, error)
	UpdateUser(ctx context.Context, userId uuid.UUID, req dtos.UserUpdate) error
	DeleteUser(ctx context.Context, userId uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	user := req.Mapper()
	err := r.db.Model(&entities.User{}).Create(&user).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) GetUserById(ctx context.Context, userId uuid.UUID) (dtos.UserRes, error) {
	var (
		user entities.User
		res  dtos.UserRes
	)
	err := r.db.Model(&entities.User{}).Where("id = ?", userId).First(&user).Error
	if err != nil {
		return res, err
	}

	res.Mapper(user)

	return res, nil
}

func (r *repository) UpdateUser(ctx context.Context, userId uuid.UUID, req dtos.UserUpdate) error {
	updates := map[string]interface{}{}

	if req.FirstName != "" {
		updates["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updates["last_name"] = req.LastName
	}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Password != "" {
		updates["password"] = utils.Bcrypt(req.Password)
	}
	if req.Language != "" {
		updates["language"] = req.Language
	}
	// Avatar alanı her zaman güncellenir (boş string ile silme işlemi için)
	updates["avatar"] = req.Avatar

	return r.db.WithContext(ctx).Model(&entities.User{}).
		Where("id = ?", userId).
		Updates(updates).Error
}

func (r *repository) DeleteUser(ctx context.Context, userId uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("id = ?", userId).
		Delete(&entities.User{}).Error
}
