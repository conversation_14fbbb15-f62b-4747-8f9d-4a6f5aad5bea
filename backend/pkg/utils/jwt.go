package utils

import (
	"fmt"
	"time"
	"whatsapp/pkg/config"

	"github.com/golang-jwt/jwt/v5"
)

type JwtCustomClaim struct {
	UserID   string
	DeviceID string
	jwt.RegisteredClaims
}

type JwtWrapper struct {
	SecretKey string
	Expire    int
}

// JwtClaim adds email as a claim to the token
type JwtClaim struct {
	ID     string
	UserId string
	jwt.RegisteredClaims
}

type JwtStoreClaim struct {
	SlugName string
	StoreId  string
	jwt.RegisteredClaims
}

func (j *JwtWrapper) GenerateJWT(phone, userId string) (string, error) {
	claims := &JwtClaim{
		UserId: userId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * time.Duration(config.ReadValue().JwtExpire))),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	signedToken, err := token.SignedString([]byte(j.<PERSON>))
	if err != nil {
		return "", err
	}
	return signedToken, nil
}

func (j *JwtWrapper) ValidateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtClaim)
	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}
	return token.Valid
}

func (j *JwtWrapper) ParseToken(tokenString string) (claims *JwtClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) GenerateStoreJWT(name, storeId string) (string, error) {
	claims := &JwtStoreClaim{
		SlugName: name,
		StoreId:  storeId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * time.Duration(config.ReadValue().JwtExpire))),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}
	return signedToken, nil
}

func (j *JwtWrapper) ValidateStoreToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtStoreClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtStoreClaim)
	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}
	return token.Valid
}

func (j *JwtWrapper) ParseStoreToken(tokenString string) (claims *JwtStoreClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtStoreClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtStoreClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtStoreClaim")
	}

	return claims, nil
}
