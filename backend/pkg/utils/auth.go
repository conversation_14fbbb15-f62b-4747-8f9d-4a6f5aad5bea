package utils

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func GetUserId(c *gin.Context) uuid.UUID {
	userId, exists := c.Get("user_id")
	if !exists {
		return uuid.UUID{}
	}

	if userUUID, ok := userId.(uuid.UUID); ok {
		return userUUID
	}

	if userStr, ok := userId.(string); ok {
		userUUID, err := uuid.Parse(userStr)
		if err == nil {
			return userUUID
		}
	}

	return uuid.UUID{}
}
