package utils

import (
	"encoding/base64"
	"fmt"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

func Bcrypt(password string) string {
	hash, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)

	return string(hash)
}

func Compare(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func DecodeShortToken(token string) (string, string, error) {
	decodedBytes, err := base64.URLEncoding.DecodeString(token)
	if err != nil {
		return "", "", err
	}

	data := string(decodedBytes)
	parts := strings.Split(data, ":")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid token format")
	}

	return parts[0], parts[1], nil
}
