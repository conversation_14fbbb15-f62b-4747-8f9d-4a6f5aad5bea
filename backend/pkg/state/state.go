package state

import (
	"context"

	"github.com/google/uuid"
)

const (
	CurrentUserID        = "CurrentUserID"
	CurrentDeviceID      = "CurrentDeviceID"
	CurrentUserIP        = "CurrentUserIP"
	CurrentTimezone      = "CurrentTimezone"
	CurrentPhoneLanguage = "CurrentPhoneLanguage"
	CurrentUserName      = "CurrentUserName"
	CurrentSlugName      = "CurrentSlugName"
	CurrentStoreID       = "CurrentStoreID"
)

func GetCurrentUserID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentUserID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentDeviceID(ctx context.Context) string {
	value := ctx.Value(CurrentDeviceID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentUserIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentTimezone(ctx context.Context) string {
	value := ctx.Value(CurrentTimezone)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPhoneLanguage(ctx context.Context) string {
	value := ctx.Value(CurrentPhoneLanguage)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentUserName(ctx context.Context) string {
	value := ctx.Value(CurrentUserName)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentSlugName(ctx context.Context) string {
	value := ctx.Value(CurrentSlugName)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentStoreID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentStoreID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}
