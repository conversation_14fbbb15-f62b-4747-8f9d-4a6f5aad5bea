package dtos

import "time"

const (
	Day = iota + 1
	Week
	Month
	ThreeMonth
)

type GenerateApiKeyDto struct {
	//UserId     string `json:"user_id"`
	ExpireTime int `json:"expire_time"`
}

func (g *GenerateApiKeyDto) CalculateExpireTime() time.Time {
	var extime time.Time
	switch g.ExpireTime {
	case Day:
		extime = time.Now().Add(24 * time.Hour)
	case Week:
		extime = time.Now().Add(24 * 7 * time.Hour)
	case Month:
		extime = time.Now().Add(24 * 30 * time.Hour)
	case ThreeMonth:
		extime = time.Now().Add(24 * 30 * 3 * time.Hour)
	}

	return extime
}

type LoginRequest struct {
	Identify string `json:"identify" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type LoginResponse struct {
	Token       string    `json:"Token"`
	Expires     time.Time `json:"Expires"`
	IsSucceeded bool      `json:"IsSucceeded"`
}
