package database

import (
	"fmt"
	"time"
	"whatsapp/pkg/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db *gorm.DB
)

func Connect(conf config.Database) {
	if db != nil {
		return
	}
	var err error
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s", conf.Host, conf.Port, conf.User, conf.Password, conf.Name)
	db, err = gorm.Open(
		postgres.New(
			postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true,
			},
		))
	if err != nil {
		panic(err)
	}
	sqlDB, err := db.DB()
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(200)
	sqlDB.SetConnMaxLifetime(time.Minute * 5)
	sqlDB.SetConnMaxIdleTime(time.Minute * 5)

	if err != nil {
		panic(err)
	}
	if conf.Migrate {
		AutoMigrate()
	}
}

func ClientDB() *gorm.DB {
	return db
}
