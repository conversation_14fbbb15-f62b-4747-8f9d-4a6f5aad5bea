package middlewares

import (
	"net/http"
	"strings"
	"whatsapp/pkg/config"
	"whatsapp/pkg/state"
	"whatsapp/pkg/utils"

	"github.com/gin-gonic/gin"
)

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {

		jwt := utils.JwtWrapper{
			SecretKey: config.ReadValue().JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			return
		}

		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserId)
			c.Set(state.CurrentUserIP, c.ClientIP())

			c.Next()
		} else {

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			return
		}
	}
}
