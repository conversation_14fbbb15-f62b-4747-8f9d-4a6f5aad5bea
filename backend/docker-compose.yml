services:
  whatsappdb:
    image: "postgres:latest"
    container_name: whatsappdb
    volumes:
      - whatsapp_data:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5465:5432"
    environment:
      - POSTGRES_USER=whatsapp
      - POSTGRES_PASSWORD=whatsapp
      - POSTGRES_DB=whatsapp
      - POSTGRES_MAX_CONNECTIONS=300
      - TZ=Europe/Istanbul
      - DEBIAN_FRONTEND=noninteractive
    networks:
      - whatsapp-net  
  whatsapp:
    build:
      context: .
      dockerfile: ./Dockerfile.dev
    image: whatsapp
    container_name: whatsapp
    restart: always
    volumes:
      - .:/app
      - ./config.docker.yaml:/app/config.yaml
      - /var/lib/docker
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    networks:
      - whatsapp-net
    ports:
      - 3010:3000

  whatsapp-web:
    container_name: whatsapp-web
    image: whatsapp-web
    build:
      context: ../frontend
      dockerfile: Dockerfile.dev
    restart: always
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
    volumes:
      - ../frontend/:/app
      - /app/node_modules
    ports:
      - "7070:7070"
    networks:
      - whatsapp-net
    depends_on:
      - whatsapp
  whatsapp_redis:
    image: redis:latest
    container_name: whatsapp_redis
    ports:
      - '6383:6379'
    volumes:
      - redis_data:/data
    environment:
    - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - whatsapp-net
 
networks:
  whatsapp-net:

volumes:
  whatsapp_data:
  redis_data:
