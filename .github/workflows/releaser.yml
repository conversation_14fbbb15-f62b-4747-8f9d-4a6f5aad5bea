name: Docker Image CI

on:
  release:
    types: [published]

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      # Set up Node.js for frontend build
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      # Build frontend
      - name: Install frontend dependencies
        run: |
          cd frontend
          npm install

      - name: Build frontend
        run: |
          cd frontend
          echo "REACT_APP_API_URL=/api/v1" > .env
          npm run build

      # Create dist directory in backend if it doesn't exist
      - name: Prepare backend dist directory
        run: |
          mkdir -p backend/dist

      # Copy frontend build to backend/dist
      - name: Copy frontend build to backend
        run: |
          cp -r frontend/build/* backend/dist/

      # Clean up .txt files
      - name: Clean up .txt files
        run: |
          find backend/dist -name "*.txt" -not -name "robots.txt" -delete

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to Docker Hub
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # Build and push Docker image
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: sametavcii/whatsapp:${{ github.ref_name }}
